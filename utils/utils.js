import config from '@/common/config/index.js';
import dayjs from '@/uni_modules/uv-ui-tools/libs/util/dayjs.js'
// var bmap = require('/static/map/bmap-wx.min.js');

/**
 * 获取当前年份的起始日期（1月1日）
 * @returns {string} - 当前年份的起始日期，格式为 "yyyy-01-01"
 */
export function getYearStartDate() {
	const now = new Date();
	const year = now.getFullYear();
	return `${year}-01-01`;
}


/**
 * 格式化时间戳为指定格式的日期字符串
 * @param {number} timestamp - 时间戳
 * @param {string} [format='yyyy-mm-dd'] - 日期格式，默认 "yyyy-mm-dd"
 * @returns {string} - 格式化后的日期字符串
 */
export function formatTimestamp(timestamp, format = 'yyyy-mm-dd') {
	const date = new Date(timestamp);

	// 获取日期相关值
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份补零
	const day = String(date.getDate()).padStart(2, '0'); // 日期补零
	const hours = String(date.getHours()).padStart(2, '0'); // 小时补零
	const minutes = String(date.getMinutes()).padStart(2, '0'); // 分钟补零
	const seconds = String(date.getSeconds()).padStart(2, '0'); // 秒补零

	// 替换格式中的占位符
	return format
		.replace('yyyy', year)
		.replace('mm', month)
		.replace('dd', day)
		.replace('hh', hours)
		.replace('ii', minutes)
		.replace('ss', seconds);
}

/**
 * 节流函数 事件触发后，限定在一定时间间隔内只能执行一次回调函数。
 * @param {Function} func - 要执行的函数
 * @param {number} delay - 执行间隔时间（毫秒）
 * @returns {Function} - 包装后的节流函数
 */
export const throttle = (func, delay = 200) => {
	let lastCall = 0;
	return (...args) => {
		const now = Date.now();
		if (now - lastCall >= delay) {
			lastCall = now;
			func(...args);
		}
	};
};
/**
 * 防抖函数 事件触发后，延迟执行回调函数。如果在延迟时间内事件再次触发，则重新计时。
 * @param {Function} func - 要执行的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} - 包装后的防抖函数
 */
export const debounce = (func, delay = 200) => {
	let timer = null;
	return (...args) => {
		if (timer) {
			clearTimeout(timer);
		}
		timer = setTimeout(() => {
			func(...args);
		}, delay);
	};
};
/**
 * 判断给定的日期是否为未来日期
 * @param {string|Date} inputDate - 输入的日期（可以是字符串或 Date 对象）
 * @returns {boolean} - 如果输入日期为未来日期，返回 true；否则返回 false
 */
export function isFutureDate(inputDate) {
	const currentDate = new Date().setHours(0, 0, 0, 0); // 当前时间的零点
	const inputDateTime = new Date(inputDate).setHours(0, 0, 0, 0); // 输入时间的零点
	return inputDateTime >= currentDate; // 比较时间
}
/**
 * 判断是否支持协访（根据用户的角色代码）
 * @param {Array} data - 用户角色数据
 * @returns {boolean} - 如果用户支持协访，返回 true；否则返回 false
 */
export function isAssistDefenseSupported(data) {
	// 10001 事业部总监
	// 10002 省区经理
	// 10003 省区主管 (协访)
	// 10005 员工
	// 10004  KA经理 (协访)
	if (data && data.length) {
		const codeList = [10003, 10004];
		return data.some(item => codeList.includes(Number(item.code)));
	} else {
		return false
	}

}
/**
 * 判断是否是 KA 经理角色
 * @param {Array} data - 用户角色数据
 * @returns {boolean} - 如果用户是 KA 经理，返回 true；否则返回 false
 */
export function isKAManager(data) {
	// 10001 事业部总监
	// 10002 省区经理
	// 10003 省区主管 (协访)
	// 10005 员工
	// 10004  KA经理 (协访)
	if (data && data.length) {
		const codeList = [10004];
		return data.some(item => codeList.includes(Number(item.code)));
	} else {
		return false
	}

}

/**
 * 将百度地图的经纬度转换为高德地图的经纬度
 * @param {Object} params - 包含百度地图坐标的参数
 * @returns {Promise} - 返回一个 Promise，成功时返回转换后的结果
 */
// export const baiduToGaoDeFun = (params) => {
// 	return baiduToGaoDe(params)
// 		.then((Result) => {
// 			return Result; // 成功时返回结果
// 		})
// 		.catch((error) => {
// 			console.error('Error:', error);
// 			throw error; // 将错误抛出，供外部捕获
// 		});
// };

/**
 * 构建查询字符串
 * @param {Object} params - 包含查询参数的对象
 * @returns {string} - 返回构建好的查询字符串
 */
export function buildQuery(params) {
	return Object.keys(params)
		.filter(key => params[key] !== undefined && params[key] !== null) // 过滤掉无效参数
		.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
		.join('&');
}
/**
 * 将秒数转换为小时和分钟的格式
 * @param {number} seconds - 时间（秒）
 * @returns {string} - 转换后的时间字符串（例如 "2小时30分钟" 或 "30分钟"）
 */
export function convertSecondsToTime(seconds) {
	if (seconds < 3600) {
		// 如果不足一小时，直接返回分钟
		return `${Math.floor(seconds / 60)}分钟`;
	} else {
		// 如果超过一小时，返回小时和分钟
		let hours = Math.floor(seconds / 3600); // 计算小时
		let minutes = Math.floor((seconds % 3600) / 60); // 计算剩余分钟
		return `${hours}小时${minutes}分钟`;
	}
}
/**
 * 检查定位授权状态
 * @returns {Promise} - 返回一个 Promise，授权成功时返回 true，失败时返回 false
 */
export function checkLocationAuth() {
	return new Promise((resolve, reject) => {
		uni.getSetting({
			success: res => {
				if (res.authSetting['scope.userLocation']) {
					// 已授权，直接返回 true
					resolve(true);
				} else {
					// 未授权，发起授权请求
					uni.authorize({
						scope: 'scope.userLocation',
						success: () => {
							// 用户授权，返回 true
							resolve(true);
						},
						fail: () => {
							// 用户拒绝授权，返回 false
							reject(false);
						}
					});
				}
			},
			fail: () => {
				// 获取设置失败，返回 false
				reject(false);
			}
		});
	});
}


/**
 * 图片压缩
 */
export const imageCompress = (url) => {
	return new Promise((resolve, reject) => {
		// #ifndef H5
		// 条件编译一下，除了H5平台，都可以使用uni 自带的图片压缩api处理
		uni.compressImage({
			src: url,
			quality: 80, // 压缩质量，范围0～100，数值越小，质量越低，压缩率越高（仅对jpg有效）
			success: async res => {
				console.log('app-----imgCompress', res.tempFilePath)
				resolve(res.tempFilePath)
			}
		})
		// #endif
		// #ifdef H5
		const img = new Image()
		img.src = url
		let files = {};
		img.onload = async () => {
			const canvas = document.createElement('canvas') // 创建Canvas对象(画布)
			const context = canvas.getContext('2d')
			// 默认按比例压缩
			let cw = img.width
			let ch = img.height
			let w = img.width
			let h = img.height
			canvas.width = w
			canvas.height = h
			if (cw > 600 && cw > ch) {
				w = 600
				h = (600 * ch) / cw
				canvas.width = w
				canvas.height = h
			}
			if (ch > 600 && ch > cw) {
				h = 600
				w = (600 * cw) / ch
				canvas.width = w
				canvas.height = h
			}
			// 生成canvas
			let base64 // 创建属性节点
			context.clearRect(0, 0, 0, w, h)
			context.drawImage(img, 0, 0, w, h)
			base64 = canvas.toDataURL()
			resolve(base64)
		}
		// #endif
	})
}

export const getSafeAreaInsetsTop = () => {
	const systemInfo = uni.getSystemInfoSync();
	const custom = uni.getMenuButtonBoundingClientRect();
	return custom.top + custom.height + (custom.top - systemInfo.statusBarHeight);
};

export const getWeekRange = (date) => {
	const curr = date ? new Date(date) : new Date();
	const day = curr.getDay();
	const diff = curr.getDate() - day + (day === 0 ? -6 : 1); // 调整周日的情况

	// 创建新的日期对象，避免修改原始日期
	const monday = new Date(curr);
	monday.setDate(diff);

	const sunday = new Date(monday);
	sunday.setDate(monday.getDate() + 6);

	return {
		start: dayjs(monday).format("YYYY-MM-DD"),
		end: dayjs(sunday).format("YYYY-MM-DD")
	};
};

// 获取月的起止日期
export const getMonthRange = (date) => {
	const curr = date ? new Date(date) : new Date();

	const year = curr.getFullYear();
	const month = curr.getMonth();
	const firstDay = new Date(year, month, 1);
	const lastDay = new Date(year, month + 1, 0);
	return {
		start: dayjs(firstDay).format("YYYY-MM-DD"),
		end: dayjs(lastDay).format("YYYY-MM-DD")
	};
};
