<template>
  <view class="selectClient-panel">
    <view class="page-content">
      <view class="top-panel">
        <view class="search-panel">
          <p-search
            placeholderText="请输入搜索内容"
            @searchSumbit="acceptSearch"
          ></p-search>
        </view>
      </view>
      <view class="shop-title">
        <text class="user">{{ bName }}</text
        ><text class="num">负责的终端（{{ total }}）</text>
      </view>
      <view v-if="shopList.length === 0" class="task-panel">
        <image class="empty" src="/static/image/bgs/empty.png" mode=""></image>
        <view class="empty-text">暂无数据<br />请联系管理员</view>
      </view>
      <view v-else class="bottom-panel">
        <scroll-view
          class="list-panel"
          scroll-y="true"
          @scrolltolower="throttledLoadMore"
          lower-threshold="200"
        >
          <view class="content-box">
            <view
              class="client-item"
              v-for="(item, index) in shopList"
              :key="item.id"
              @tap="triggerClient(item)"
              :data-item="item"
            >
              <view class="left-panel">
                <text class="title">{{ item.name }}</text>
                <text class="address">{{ item.locationList[0]?.address }}</text>
                <text class="desc">
                  距您：
                  <text class="num">{{ item.distance || "-" }}KM</text>
                </text>
              </view>
              <view class="right-panel">
                <image
                  class="icon"
                  src="/static/image/icon/comm/icon_arrow_right.png"
                  mode=""
                ></image>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      <view v-if="loading" class="uv-picker--loading">
        <uv-loading-icon mode="circle"></uv-loading-icon>
      </view>
      <view class="placeholder-view"></view>
      <p-visit></p-visit>
    </view>
    <p-tabbar tabbarSelect="work"></p-tabbar>
  </view>
</template>

<script setup>
import { onLoad, onShow } from "@dcloudio/uni-app";
import { ref, reactive } from "vue";
import { crmTerminalWechatVo } from "/common/api/sign/index.js";
import { useVisitStore } from "@/common/store/visit";
import { useUserStore } from "@/common/store/user";
import { fetchUserLocation } from "/utils/bMapUtils.js";
import { USER_DETAIL_KEY, USER_INFO_KEY } from "/common/const/cache.js";
const userDetail = uni.getStorageSync(USER_DETAIL_KEY);
const UserInfo = uni.getStorageSync(USER_INFO_KEY);

const keyword = ref(null);
const shopList = ref([]);
const loading = ref(false);
const pagination = reactive({
  currentPage: 1,
  totalItems: 0,
  pageSize: 20,
});
const bName = ref("");
const total = ref(0);
const visitStore = useVisitStore();
const userStore = useUserStore();
// 用户当前地理位置的经纬度 百度地图
const latitude = ref(null);
const longitude = ref(null);

const latitudeBaidu = ref(null);
const longitudeBaidu = ref(null);
const personId = ref(null);
const throttledLoadMore = () => {
  if (loading.value) return;
  pagination.currentPage++;
  getShopList("", latitudeBaidu.value, longitudeBaidu.value, personId.value);
};
// 初始化用户的本地位置信息
const initUserLocal = (terminalUserId) => {
  // 创建百度地图对象，使用指定的 ak（API Key）
  fetchUserLocation(
    ({ baidu, gaode, address }) => {
      latitudeBaidu.value = baidu.latitude; // 给经纬度赋值
      longitudeBaidu.value = baidu.longitude; // 给经纬度赋值

      latitude.value = gaode.latitude; // 获取纬度
      longitude.value = gaode.longitude; // 获取经度
      getShopList("", baidu.latitude, baidu.longitude, terminalUserId); // 获取商户列表
    },
    (error) => {
      // 错误处理
      console.error("位置获取失败:", error);
    }
  );
};
const acceptSearch = (inptKeyword) => {
  console.log("搜索条件：keyword", inptKeyword);
  pagination.currentPage = 1;
  shopList.value = [];
  getShopList(
    inptKeyword,
    latitudeBaidu.value,
    longitudeBaidu.value,
    personId.value
  );
};
const getShopList = async (name, latitude, longitude, terminalUserId) => {
  if (loading.value) {
    return;
  }
  loading.value = true;
  try {
    let parmas = {
      // deptId: userStore.getUserInfo.departmentId,
      // departmentId: userStore.getUserInfo.departmentId,
      // postCode: userStore.getUserInfo.posts[0].code,
      // postId: userStore.getUserInfo.posts[0].id,
      terminalUserId: terminalUserId
        ? terminalUserId
        : userStore.getUserInfo.id,
      terminalName: name || "",
      limit: pagination.currentPage,
      size: pagination.pageSize,
      latitude,
      longitude,
    };
    console.log(userStore.getUserInfo);
    const api = crmTerminalWechatVo;
    let { code, data } = await api(parmas);
    loading.value = false;
    if (code == 0) {
      shopList.value = shopList.value.concat(data.list || []);
      total.value = data.total || 0;
    }
  } catch (error) {
    console.error("Error:", error);
  }
};

const triggerClient = (item) => {
  visitStore.setClientShop(item);
  visitStore.setClientDoctor(null);
  visitStore.setClientDept(null);
  uni.navigateTo({
    url: "/pages/subVisit/selectClientDoctor/selectClientDoctor",
  });
};

onLoad((option) => {
  const { terminalUserId, personName } = option;
  personId.value = terminalUserId;
  bName.value = personName ? personName : userDetail.name;
  initUserLocal(terminalUserId);
});
onShow(() => {
  visitStore.setActiveTab(0);
});
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";
.placeholder-view {
  width: 100%;
  height: 60rpx;
}
.selectClient-panel {
  .shop-title {
    @include cardBgCommonStyle();
    margin: 20rpx 20rpx 0;
    display: inline-flex;
    padding: 20rpx 0 20rpx 24rpx;
    align-items: flex-start;
    .user {
      color: var(----, #0d6ce4);

      /* 点文本-加粗/14pt bold */
      font-family: "PingFang SC";
      font-size: 28rpx;
      font-style: normal;
      font-weight: 600;
      margin-right: 24rpx;
    }
    .num {
      color: var(---Gray6, #8d9094);

      /* 点文本-常规/14pt regular */
      font-family: "PingFang SC";
      font-size: 28rpx;
      font-style: normal;
      font-weight: 400;
    }
  }
  .page-content {
    @include globalPageStyle();
    display: flex;
    flex-direction: column;

    .top-panel {
      z-index: 1;
      padding: 26rpx 24rpx 18rpx;
      box-sizing: border-box;
      background: rgba(255, 255, 255, 0.9);
      /* 下层投影 */
      box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(0, 0, 0, 0.12);
      display: flex;
      flex-direction: column;

      .search-panel {
        ::v-deep .search-panel {
          // display: none;
          background: transparent;
          padding: 0;
          box-shadow: none;
        }
      }
    }

    .bottom-panel {
      flex: 1;
      overflow-y: hidden;

      .list-panel {
        box-sizing: border-box;
        padding: 28rpx 20rpx;
        padding-right: 0;
        height: 100%;

        .content-box {
          box-sizing: border-box;
          padding-right: 20rpx;
          height: 100%;
        }

        .client-item {
          @include cardBgCommonStyle();
          height: 188rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          margin-bottom: 24rpx;
          box-sizing: border-box;
          padding: 20rpx 24rpx;

          .left-panel {
            display: flex;
            flex-direction: column;

            .title {
              @include setBoldFont(28rpx, 44rpx, #1d1d1d);
            }

            .address,
            .desc {
              margin-top: 12rpx;
              @include setlightFont(24rpx, 40rpx, rgba(29, 29, 29, 0.6));

              .num {
                @include setlightFont(24rpx, 40rpx, #2f3133);
              }
            }
          }

          .right-panel {
            width: 32rpx;
            height: 32rpx;
          }

          .icon {
            width: 15rpx;
            height: 24rpx;
          }
        }

        // .empty-step {
        // 	height: 100%;
        // 	width: 100%;
        // }
        // .loading-list-panel {
        // 	height: 100%;
        // 	width: 100%;
        // 	display: flex;
        // 	justify-content: center;
        // 	align-items: center;
        // }
      }
    }
  }
  .empty {
    width: 230rpx;
    height: 200rpx;
    margin: 26vh auto 20rpx;
    display: flex;
  }
  .empty-text {
    color: var(---white, #fff);
    text-align: center;

    /* 点文本-加粗/12pt bold */
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 166.667% */
  }
}
</style>
