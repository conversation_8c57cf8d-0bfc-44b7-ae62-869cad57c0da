<template>
	<view class="bottom-tabbar" :fixed="true">
		<uv-tabbar :value="tabbarSelect" activeColor="#4068F5" inactiveColor="#B7B7B7" @change="onchange" :border="false" safeAreaInsetBottom iconSize="48rpx">
			<uv-tabbar-item text="首页" name="home">
				<template v-slot:active-icon>
					<image class="icon" src="/static/image/tabbar/home_checked.png"></image>
				</template>
				<template v-slot:inactive-icon>
					<image class="icon" src="/static/image/tabbar/home.png"></image>
				</template>
			</uv-tabbar-item>
			<uv-tabbar-item text="工作台" name="work">
				<template v-slot:active-icon>
					<image class="icon" src="/static/image/tabbar/work_checked.png"></image>
				</template>
				<template v-slot:inactive-icon>
					<image class="icon" src="/static/image/tabbar/work.png"></image>
				</template>
			</uv-tabbar-item>
			<uv-tabbar-item text="我的" name="mine">
				<template v-slot:active-icon>
					<image class="icon" src="/static/image/tabbar/mine_checked.png"></image>
				</template>
				<template v-slot:inactive-icon>
					<image class="icon" src="/static/image/tabbar/mine.png"></image>
				</template>
			</uv-tabbar-item>
		</uv-tabbar>
	</view>
</template>
<!-- TODO:这个文件里选中后的icon要换掉 -->
<script setup>
import { defineProps } from 'vue';

const props = defineProps({
	tabbarSelect: {
		type: [String],
		required: true
	}
});

const onchange = (event) => {
	const { tabbarSelect } = props;
	if (tabbarSelect !== event) {
		let url = '';
		switch (event) {
			case 'work':
				// 如果event为'work'，设置要跳转的页面路径为'/pages/workbenches/index'
				url = '/pages/workbenches/workbenches';
				break;
			case 'home':
				// 如果event为'home'，设置要跳转的页面路径为'/pages/home/<USER>'
				url = '/pages/home/<USER>';
				break;
			case 'mine':
				// 如果event为'mine'，设置要跳转的页面路径为'/pages/mine/index'
				url = '/pages/mine/mine';
				break;
			default:
				break;
		}
		if (url) {
			uni.switchTab({
				url
			});
		}
	}
};
</script>

<style scoped lang="scss">
@import '@/static/scss/global.scss';
.icon {
	width: 48rpx;
	height: 48rpx;
}
.bottom-tabbar {
	:deep() {
		.uv-tabbar__content__item-wrapper {
			height: 126rpx !important;
		}
		.uv-tabbar-item__text {
			@include setlightFont(20rpx, 34rpx);
		}
	}
}
</style>
