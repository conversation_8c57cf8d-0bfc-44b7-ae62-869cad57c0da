<template>
  <view class="login-page">
    <view
      :style="{ height: `${safeAreaInsetsTop ? safeAreaInsetsTop : 0}px` }"
    ></view>
    <view class="logo-panel">
      <image
        src="/static/image/icon/comm/icon_logo.png"
        mode=""
        class="logo"
      ></image>
    </view>
    <view class="welcoming-speech">
      你好，
      <br />
      欢迎使用业务员助手
    </view>
    <view class="login-content">
      <view class="account" v-if="state.type == LoginType.ACCOUNT">
        <view class="form-item uname">
          <image
            class="icon"
            src="/static/image/icon/login/icon_user.png"
            mode=""
          ></image>
          <view class="input-panel">
            <van-field
              v-model="state.accountLogin.userName"
              placeholder="请输入登录账号或手机号"
              border="{{ false }}"
              @blur="inputUserName"
              maxlength="11"
              clearable
            />
          </view>
        </view>
        <view class="form-item paw">
          <image
            class="icon"
            src="/static/image/icon/login/icon_password.png"
            mode=""
          ></image>
          <view class="input-panel">
            <van-field
              v-model="state.accountLogin.password"
              placeholder="请输入密码"
              border="{{ false }}"
              @change="(event) => (state.accountLogin.password = event.detail)"
              clearable
              :password="state.showPsd ? false : true"
            />
          </view>
          <view
            class="right-icon"
            :class="state.showPsd ? 'yanjing' : 'yanjing1'"
            @tap="state.showPsd = !state.showPsd"
          ></view>
        </view>
      </view>

      <view class="account" v-if="state.type == LoginType.PHONE">
        <view class="form-item phone">
          <image
            class="icon"
            src="/static/image/icon/login/icon_shouji.png"
            mode=""
          ></image>
          <view class="input-panel">
            <van-field
              v-model="state.phoneLogin.mobile"
              placeholder="请输入您的手机号码"
              border="{{ false }}"
              @change="(event) => (state.accountLogin.mobile = event.detail)"
              maxlength="11"
              clearable
            />
          </view>
        </view>
        <view class="form-item sms">
          <image
            class="icon"
            src="/static/image/icon/login/icon_yanzhengma.png"
            mode=""
          ></image>
          <view class="input-panel">
            <van-field
              v-model="state.phoneLogin.code"
              placeholder="请输入验证码"
              border="{{ false }}"
              @change="(event) => (state.accountLogin.code = event.detail)"
              clearable
            />
          </view>
          <view
            class="item-btn sendsmsBtn"
            v-if="state.phoneMs.waitingTime >= 60"
            @tap="sendSMS"
          >
            <text>{{ state.phoneMs.sendMsText }}</text>
          </view>
          <view class="item-btn" v-else>
            <text>{{ state.phoneMs.waitingTime }}</text>
          </view>
        </view>
      </view>

      <view class="privacyPolicy">
        <view
          @tap="permission = !permission"
          class="circle"
          :class="permission ? 'select' : ''"
        ></view>
        <view class="text-panel">
          我已阅读并同意
          <text class="stress">《用户协议》</text>
          和
          <text class="stress">《隐私政策》</text>
        </view>
      </view>

      <view class="login-button">
        <view class="button" @tap="bindLogin()">登录</view>
      </view>

      <view class="tips-panel">
        <!--				<view class="loginCaptcha" @tap="changeType">{{ loginText }}</view>-->
        <view class="tip">仅供康缘员工使用</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { LoginType } from "/common/enums/index.ts";
const { safeAreaInsets } = uni.getSystemInfoSync();
import { ref, reactive, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import CryptoJS from "crypto-js";
import { sendSmsApi } from "@/common/api/login";
import { useUserStore } from "@/common/store/user";
const userStore = useUserStore();
// 安全区域高度
const safeAreaInsetsTop = ref(0);

// 登录状态
const state = reactive({
  type: LoginType.ACCOUNT,
  showPsd: false,
  accountLogin: {
    userName: "",
    password: "",
  },
  phoneLogin: {
    mobile: "",
    code: "",
  },
  phoneMs: {
    disabled: true,
    sendMsText: "发送验证码",
    waitingTime: 60,
    coolDownTime: 60,
  },
  tenantCode: "",
});
const inputUserName = (e) => {
  console.log(e.detail, "---e.detail");

  state.accountLogin.userName = e.detail.value;
};
const permission = ref(false);
const loading = ref(false);
// 计算 `登录方式文本`
const loginText = computed(() =>
  state.type === LoginType.ACCOUNT ? "验证码登录" : "账号登录"
);

onLoad(() => {
  // 初始化时计算 `safeAreaInsetsTop`
  uni.getSystemInfo({
    success: (e) => {
      const custom = uni.getMenuButtonBoundingClientRect(); // 获取菜单按钮信息
      safeAreaInsetsTop.value =
        custom.top + custom.height + (custom.top - e.statusBarHeight);
    },
  });
});

/**
 * @description 切换登录方式
 */
const changeType = () => {
  state.type =
    state.type === LoginType.ACCOUNT ? LoginType.PHONE : LoginType.ACCOUNT;
};
/**
 * @description 发送验证码
 */
const sendSMS = async () => {
  if (state.phoneMs.waitingTime >= 60) {
    let checked = checkMobile();
    if (checked) {
      try {
        let res = await sendSmsApi(state.phoneLogin.mobile);
        if (res) {
          changeWaitingTime();
          uni.showToast({
            title: "发送成功",
            icon: "none",
            position: "top",
          });
        }
      } catch (error) {
        uni.showToast({
          title: "发送失败",
          icon: "none",
          position: "top",
        });
      }
    } else {
      uni.showToast({
        title: "请输入正确的手机号",
        icon: "none",
        position: "top",
      });
    }
  }
};
/**
 * @description 倒计时管理（递归）
 */
const changeWaitingTime = () => {
  if (state.phoneMs.waitingTime == 0) {
    state.phoneMs.waitingTime = state.phoneMs.coolDownTime;
  } else {
    state.phoneMs.waitingTime--;
    setTimeout(() => {
      changeWaitingTime();
    }, 1000);
  }
};
/**
 * @description 手机号校验
 * @returns {Boolean} 是否有效
 */
const checkMobile = () => /^1\d{10}$/.test(state.phoneLogin.mobile);

/**
 * @description 登录处理
 */
const bindLogin = () => {
  console.log(state, "---state");
  if (state.type == LoginType.PHONE) {
    phoneSubmit();
  } else if (state.type == LoginType.ACCOUNT) {
    accountSubmit();
  }
};
// 手机号登录
const phoneSubmit = async () => {
  if (!checkMobile()) {
    uni.showToast({
      title: "请输入正确的手机号",
      icon: "none",
      position: "top",
    });
    return;
  }
  // 检查用户是否已勾选《用户协议》和《隐私政策》
  if (!permission.value) {
    uni.showToast({
      icon: "none",
      title: "阅读并同意《用户协议》和《隐私政策》",
    });
    return;
  }
  if (state.phoneLogin.code.length < 6) {
    uni.showToast({
      icon: "none",
      title: "验证码不正确",
    });
    return;
  }
  loading.value = true;
  try {
    state.phoneLogin.deviceType = 1; //pc-0, app-1
    await userStore.phoneLogin(state.phoneLogin);
    loading.value = false;
  } catch (error) {
    loading.value = false;
  }
};

function btoa(input) {
  const chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
  let str = String(input);
  let output = "";
  for (
    let block = 0, charCode, idx = 0, map = chars;
    str.charAt(idx | 0) || ((map = "="), idx % 1);
    output += map.charAt(63 & (block >> (8 - (idx % 1) * 8)))
  ) {
    charCode = str.charCodeAt((idx += 3 / 4));
    if (charCode > 0xff) {
      throw new Error(
        '"btoa" failed: The string to be encoded contains characters outside of the Latin1 range.'
      );
    }
    block = (block << 8) | charCode;
  }
  return output;
}
// 账号登录
async function accountSubmit() {
  // 检查用户是否已勾选《用户协议》和《隐私政策》
  if (!permission.value) {
    uni.showToast({
      icon: "none",
      title: "阅读并同意《用户协议》和《隐私政策》",
    });
    return;
  }
  // 检查账号是否为空
  if (
    state.accountLogin.userName == "" ||
    /[^a-zA-Z0-9]/g.test(state.accountLogin.userName)
  ) {
    uni.showToast({
      icon: "none",
      title: "账号不正确",
    });
    return;
  }
  // 检查密码是否为空
  if (state.accountLogin.password.length == "") {
    uni.showToast({
      icon: "none",
      title: "密码不正确",
    });
    return;
  }
  // 设置对称加密的密钥和加密选项
  const key = CryptoJS.enc.Utf8.parse("****************");
  const options = {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  };
  // 对称加密
  // 使用 AES 加密密码，并将加密结果编码为 Base64 格式
  const password = btoa(
    unescape(
      encodeURIComponent(
        CryptoJS.AES.encrypt(state.accountLogin.password, key, options)
      )
    )
  );
  loading.value = true;
  try {
    // 设置设备类型（0: pc, 1: app）
    state.accountLogin.deviceType = 1; //pc-0, app-1
    // 构造登录参数，包含加密后的密码
    const params = {
      ...state.accountLogin,
      password,
    };
    // 调用登录接口
    await userStore.login(params);
    loading.value = false;
  } catch (error) {
    loading.value = false;
  }
}
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";
.login-page {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  .logo-panel {
    box-sizing: border-box;
    padding: 0 50rpx;
    .logo {
      width: 120rpx;
      height: 114rpx;
    }
  }
  .welcoming-speech {
    box-sizing: border-box;
    padding: 0 50rpx;
    @include setBoldFont(40rpx, 52rpx, #fff);
    margin-top: 40rpx;
  }
  .login-content {
    position: relative;
    z-index: 2;
    margin-top: 40rpx;
    flex: 1;
    border-radius: 48rpx;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding: 76rpx 38rpx 0;
    .form-item {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      box-sizing: border-box;
      width: 100%;
      border-bottom: 2rpx solid #ced4db;
      padding: 16rpx 0;

      .icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: 18rpx;
      }
      .input-panel {
        flex: 1;
        :deep() {
          .van-cell {
            --cell-vertical-padding: 0;
            --cell-horizontal-padding: 0;
            --cell-background-color: transparent;
            --cell-font-size: 28rpx;
            --cell-line-height: 44rpx;
            @include setlightFont(32rpx, 28rpx, #1d1d1d);
          }
          .van-field__placeholder {
            @include setlightFont(32rpx, 28rpx, #8d9094);
          }
        }
      }
      .item-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        &.sendsmsBtn {
          border-radius: 48rpx;
          background: #7eadfd;
        }
        text {
          padding: 4rpx 24rpx;
          @include setBoldFont(24rpx, 48rpx, #fff);
        }
      }
      .right-icon {
        width: 48rpx;
        height: 48rpx;
        &.yanjing {
          @include setBackgroundImage(
            "/static/image/icon/login/icon_visible.png"
          );
        }
        &.yanjing1 {
          @include setBackgroundImage("/static/image/icon/login/icon_hide.png");
        }
      }

      &.uname,
      &.phone {
        margin-bottom: 78rpx;
      }
      // &.paw {
      // 	margin-bottom: ;
      // }
    }

    .privacyPolicy {
      margin-top: 56rpx;
      display: flex;
      justify-content: flex-start;
      .circle {
        width: 32rpx;
        height: 32rpx;
        margin-right: 26rpx;
        @include setBackgroundImage(
          "/static/image/icon/login/icon_home_radio.png"
        );
        &.select {
          @include setBackgroundImage(
            "/static/image/icon/login/icon_home_radio_checked.png"
          );
        }
      }
      .text-panel {
        @include setBoldFont(24rpx, 32rpx, rgba(29, 29, 29, 0.6));
        .stress {
          @include setBoldFont(24rpx, 32rpx, #4684fe);
        }
      }
    }

    .login-button {
      display: flex;
      justify-content: center;
      margin-top: 192rpx;
      .button {
        @include commonButtonStyle();
        width: 566rpx;
        height: 84rpx;
        line-height: 84rpx !important;
      }
    }
    .tips-panel {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: absolute;
      bottom: calc(env(safe-area-inset-bottom) + 60rpx);
      left: 0;
      right: 0;
      .loginCaptcha {
        @include setBoldFont();
        margin-bottom: 18rpx;
      }
      .tip {
        @include setlightFont(28rpx, 36rpx, rgba(29, 29, 29, 0.6));
      }
    }
  }
}
</style>
s
