@import '@/static/scss/global.scss';
.week-content {
	background: rgba(255, 255, 255, 0.9);

	/* 下层投影 */
	box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(0, 0, 0, 0.12);
}
.controller {
	position: relative;
	margin-bottom: 24rpx;
	display: flex;
	justify-content: space-between;
	box-sizing: border-box;
	padding: 12rpx 32rpx;
	.today-btn {
		position: absolute;
		right: 40rpx;
		top: 50%;
		transform: translateY(-50%);
		font-size: 24rpx;
	}
	.action {
		// display: flex;
		// justify-content: center;
		// align-items: center;
		.current-current {
			@include setlightFont(20rpx, 26rpx, #1d1d1d);
		}
		// .date {
		// 	width: 240rpx;
		// 	font-size: 36rpx;
		// 	text-align: center;
		// }
		// .arrow {
		// 	width: 100rpx;
		// 	height: 40rpx;
		// 	text-align: center;
		// 	&.disabled {
		// 		opacity: 0.2;
		// 	}
		// 	&.right {
		// 		transform: rotate(180deg);
		// 	}
		// 	.icon {
		// 		width: 36rpx;
		// 		height: 36rpx;
		// 		margin: 0 auto;
		// 		display: flex;
		// 		justify-content: center;
		// 		align-items: center;
		// 		position: relative;
		// 		&::after {
		// 			content: '';
		// 			height: 16rpx;
		// 			width: 16rpx;
		// 			top: 12rpx;
		// 			border-width: 0 0 2rpx 2rpx;
		// 			border-color: #000;
		// 			border-style: solid;
		// 			transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
		// 			position: absolute;
		// 		}
		// 	}
		// }
	}
}

.calender {
	// height: 622rpx;
	overflow: hidden;
	transition: height 0.2s ease;
	&.month {
		height: 622rpx;
	}
	&.week {
		// height: 150rpx;
	}
	.week-box {
		display: grid;
		grid-template-columns: repeat(7, 1fr);
		font-weight: bold;
		.week {
			width: 70rpx;
			text-align: center;
			margin: 0 auto;
			@include setBoldFont(26rpx, 33.8rpx, rgba(29, 29, 29, 0.6));
		}
	}

	.day-box {
		margin-top: 20rpx;
		position: relative;
	}

	.days,
	.placeholder {
		display: grid;
		grid-template-columns: repeat(7, 1fr);
		row-gap: 16rpx;
		font-weight: bold;

		.item {
			border-radius: 16rpx;
			width: 80rpx;
			height: 80rpx;
			text-align: center;
			background: #f3f3f3;
			transition: all 0.2s ease;
			margin: 0 auto;
			position: relative;
			text-align: center;
			@include setBoldFont(32rpx, 80rpx, #303030);
			&.today {
				border-radius: 16rpx;
				background: #f1f1f1 !important;
			}

			&.in {
				background: #f2f2ff;
			}

			&.after,
			&.before,
			&.prev,
			&.next {
				background: transparent !important;
				color: #d2d2d2 !important;
			}

			&.active {
				background: var(--active-bg) !important;
				color: var(--active) !important;
				transition: all 0.1s ease;
			}
			.dots {
				width: 8rpx;
				height: 8rpx;
				border-radius: 50%;
				background: red;
				position: absolute;
				left: 50%;
				bottom: 10rpx;
				transform: translateX(-50%);
			}
		}
	}

	.days {
		opacity: 1;
		transform: translateX(0);
		position: relative;
		z-index: 3;

		&.prev,
		&.next {
			animation: switch 0.3s ease forwards;
			animation-delay: 0.1s;
		}

		&.prev {
			transform: translateX(-100vw);
		}

		&.next {
			transform: translateX(100vw);
		}
	}

	.placeholder {
		position: absolute;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		z-index: 1;
		opacity: 0;

		&.prev,
		&.next {
			animation: opacity0 0.3s ease forwards;
		}
	}
}

.view-change-btn {
	text-align: center;
	color: #888;
	font-size: 26rpx;
	padding: 12rpx 20rpx 4rpx;
	position: relative;
	.open-icon {
		width: 52rpx;
		height: 8rpx;
		border-radius: 18rpx;
		background: #d9d9d9;
		margin: 0 auto;
	}
	&::before,
	&::after {
		content: '';
		width: 260rpx;
		height: 2rpx;
		background: #f7f8f9;
		position: absolute;
		top: 50%;
		z-index: 1;
	}

	&::before {
		left: 50rpx;
	}

	&::after {
		right: 50rpx;
	}
}

@keyframes switch {
	0% {
		opacity: 0;
	}

	40% {
		opacity: 0;
	}

	100% {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes opacity0 {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0;
	}
}
