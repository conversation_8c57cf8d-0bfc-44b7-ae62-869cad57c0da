import config from '@/common/config/index.js';
import {
	TOKEN_KEY
} from '../../const/cache.js';
export const uploadPic = (file) => {
	const token = uni.getStorageSync(TOKEN_KEY)
	console.log(token, '动态获取获取');
	let header = {
		'mediaType': "MINI_PROGRAM",
		'Authorization': 'Bearer ' + token,
		'Content-type': 'multipart/form-data',
		'accept': "application/json"
	};
	return new Promise((resolve, reject) => {
		console.log('获取请求时携带的header', header);
		wx.uploadFile({
			url: `${config.baseUrl}/system/oss/multi-upload`,
			filePath: file,
			name: 'file',
			header: header,
			formData: {
				file: file,
			},
			success(res) {
				resolve(res.data)
			},
			fail(err) {
				reject(err)
			}
		})
	})
}
