/**
 * 按照 typeCode 对数据进行分组
 * @param {Array} data - 原始数据数组
 * @returns {Object} 分组后的数据对象
 */
export function groupByTypeCode(data) {
  if (!Array.isArray(data)) {
    return {};
  }

  return data.reduce((groups, item) => {
    const typeCode = item.typeCode;
    
    if (!groups[typeCode]) {
      groups[typeCode] = {
        typeCode: typeCode,
        typeName: item.typeName,
        typeSort: item.typeSort,
        items: []
      };
    }
    
    groups[typeCode].items.push(item);
    
    return groups;
  }, {});
}

/**
 * 将分组后的对象转换为数组格式
 * @param {Object} groupedData - 分组后的数据对象
 * @returns {Array} 分组后的数据数组
 */
export function groupedDataToArray(groupedData) {
  return Object.values(groupedData).sort((a, b) => a.typeSort - b.typeSort);
}

/**
 * 一步完成分组并转换为数组
 * @param {Array} data - 原始数据数组
 * @returns {Array} 分组后的数据数组
 */
export function groupDataByTypeCode(data) {
  const grouped = groupByTypeCode(data);
  return groupedDataToArray(grouped);
}

// 示例用法
const exampleData = [
  {
    "id": "1902910513685303297",
    "typeSort": 1,
    "name": "医院上层院领导关系",
    "typeCode": "zdzy",
    "typeName": "终端资源"
  },
  {
    "id": "1902910513970515970", 
    "typeSort": 1,
    "name": "医院目标科KOL/VIP医生数",
    "typeCode": "zdzy",
    "typeName": "终端资源"
  },
  {
    "id": "1902910514041819138",
    "typeSort": 2,
    "name": "医院代表数", 
    "typeCode": "qyyygwyj",
    "typeName": "签约医院过往业绩"
  }
];

// 使用示例：
// const groupedData = groupDataByTypeCode(exampleData);
// console.log(groupedData);

/* 
输出结果：
[
  {
    typeCode: "zdzy",
    typeName: "终端资源", 
    typeSort: 1,
    items: [
      { id: "1902910513685303297", name: "医院上层院领导关系", ... },
      { id: "1902910513970515970", name: "医院目标科KOL/VIP医生数", ... }
    ]
  },
  {
    typeCode: "qyyygwyj",
    typeName: "签约医院过往业绩",
    typeSort: 2, 
    items: [
      { id: "1902910514041819138", name: "医院代表数", ... }
    ]
  }
]
*/
