import {
    http
} from '@/common/request/index.js'; // 局部引入

const api = {
    getAgentList: '/bdm/agentevaluation/page',
    addAgent: '/bdm/agent/add',
    editAgent: '/bdm/agent/edit',
    deleteAgent: '/bdm/agent/delete',
    info: '/bdm/agent/info',
    year: '/bdm/payer/years',
    getProvince: '/magic-api/common/getProvince',
    getCityByProvinceCode: '/magic-api/common/getCityByProvinceCode',
    getPreAgentSelect: '/bdm/agent/getPreAgentSelect',
    getAgentTerminal: '/bdm/agentterminalrel/page',
    getInfoWithEnable: 'base/agentevaltemplate/getInfoWithEnable',
    getAgentSelect: '/magic-api/common/getAgentSelect',
    createAgentEvaluationDraft: '/bdm/agentevaluation/create'
}

// 获取代理商列表
export const getAgentList = (params) => {
    return http.get(api.getAgentList, { params });
}

export const getProvince = (params) => {
    // 省
    return http.get(api.getProvince, { params });
}

export const getCityByProvinceCode = (params) => {
    // 市
    return http.get(api.getCityByProvinceCode, { params });
}

export const getYear = () => {
    // 市
    return http.get(api.year);
}

export const getPreAgentSelect = (params) => {
    return http.get(api.getPreAgentSelect, { params });
}

export const getAgentTerminal = (params) => {
    return http.get(api.getAgentTerminal, { params });
}

export const getInfoWithEnable = (params) => {
    return http.get(api.getInfoWithEnable, { params });
}

export const getAgentSelect = (params) => {
    return http.get(api.getAgentSelect, { params });
}

export const createAgentEvaluationDraft = (params) => {
    return http.post(api.createAgentEvaluationDraft, params);
}