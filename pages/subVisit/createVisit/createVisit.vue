<template>
  <view class="createVisit-page">
    <uv-loading-page
      :loading="loadingPage"
      loading-mode="spinner"
      loading-text="加载中..."
    ></uv-loading-page>
    <view class="content-panel">
      <view class="top-panel">
        <!-- 拜访打卡 -->
        <!-- <view
          class="top-select-panel"
          v-if="pageType === 'visit'"
          @tap="triggerVisitType"
        >
          <view class="text-left">
            <text class="right-title">拜访类型：</text>
            <text class="right-desc">{{
              selectVisit ? selectVisit.name : "请选择"
            }}</text>
          </view>
          <view class="icon-right">
            <uv-icon size="30rpx" name="arrow-right"></uv-icon>
          </view>
        </view> -->
        <!-- 协访打卡 -->
        <view class="top-select-panel" v-if="visitType !== '3'">
          <view class="text-left">
            <text class="right-title require">{{ firstTitle }}：</text>
          </view>
          <!-- v-if="clientDoctor" -->
          <view
            class="text-right"
            v-if="clientShop.locationList && clientShop.locationList.length > 0"
          >
            <view class="right-desc doctorName" @click.stop="triggerDoctor">
              {{ clientDoctor }}
              <view class="icon-right">
                <uv-icon size="30rpx" name="arrow-right"></uv-icon>
              </view>
            </view>
            <view class="right-desc right-address" v-if="siginType != 1">{{
              clientShop.address
            }}</view>
            <view
              v-else
              :style="{
                maxHeight: visitType == 2 ? '255rpx' : '480rpx',
                'overflow-y': 'auto',
              }"
            >
              <van-radio-group
                :value="addressCheck"
                @change="throttleAddressChange"
              >
                <view
                  class="address-line"
                  v-for="(item, index) in clientShop.locationList"
                  :key="index"
                >
                  <view class="address-line-left">
                    <van-radio
                      :name="item.id"
                      shape="square"
                      icon-size="32rpx"
                    ></van-radio>
                    <view>
                      <view class="desc">{{ item.remark }}</view>
                      <view class="address">{{ item.address }}</view>
                    </view>
                  </view>
                  <view
                    v-if="addressCheck === item.id && showUpload"
                    class="right-location location_upload"
                    @click.stop="handleUploadLocation(item)"
                  >
                    上报地址
                  </view>
                </view>
              </van-radio-group>
            </view>
            <!--  v-if="showUpload" -->
          </view>
          <view class="text-right" v-else>
            <view class="right-desc doctorName" @click.stop="triggerDoctor">
              {{ clientDoctor }}
              <view class="icon-right">
                <uv-icon size="30rpx" name="arrow-right"></uv-icon>
              </view>
            </view>
            <view class="right-desc right-address">{{
              clientShop.address
            }}</view>
            <!--  v-if="showUpload" -->
            <view
              class="right-location location_upload"
              @click.stop="handleUploadLocation"
            >
              上报地址
            </view>
          </view>
        </view>
        <!-- v-if="clientShop && isManage()" -->
        <view class="top-select-panel" v-if="visitType === '2'">
          <view class="text-left">
            <text class="right-title">招商代表：</text>
          </view>
          <view class="text-right">
            <view class="right-desc">{{ zsdbName }}</view>
            <view class="right-desc right-address">{{ zsdbPost }}</view>
            <view class="scene">
              <van-checkbox
                icon-size="32rpx"
                shape="square"
                :value="isScene"
                @change="onChange"
              >
                <text
                  class="scene-text"
                  :style="{ color: isScene ? '#0D6CE4' : '#FC474C' }"
                  >{{ isScene ? "在场" : "缺席" }}</text
                >
              </van-checkbox>
              <text class="tip">请确认下属员工是否在场</text>
            </view>
          </view>
        </view>
        <!-- v-if="clientShop" -->
        <view class="top-select-panel">
          <view class="text-left">
            <text class="right-title" :class="{ require: visitType === '3' }"
              >{{ agentTitle }}：</text
            >
          </view>
          <view class="text-right">
            <view class="text-right">
              <view
                v-if="isAgent"
                class="right-desc doctorName"
                @click.stop="triggerAgentChange"
                ><text class="agentPlace agentShang" v-if="agentShang">{{
                  agentShang
                }}</text
                ><text class="agentPlace" v-else>请选择代理商</text
                ><view class="icon-right">
                  <uv-icon size="30rpx" name="arrow-right"></uv-icon> </view
              ></view>
              <view class="scene" v-if="visitType !== '3'">
                <van-checkbox
                  icon-size="32rpx"
                  shape="square"
                  :value="isAgent"
                  @change="onAgentChange"
                >
                  <text
                    class="scene-text"
                    :style="{ color: isAgent ? '#0D6CE4' : '#FC474C' }"
                    >{{ isAgent ? "在场" : "缺席" }}</text
                  >
                </van-checkbox>
                <text class="tip">请确认代理商是否在场</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="map-panel">
        <map
          name="visit-map-panel"
          id="mapPanel"
          class="map-content"
          scale="17"
          :latitude="latitude"
          :longitude="longitude"
          :enable-rotate="false"
          :enable-scroll="false"
          :enable-zoom="false"
        ></map>
        <view class="obscuration">
          <view class="content">
            <image
              class="img"
              @click="play"
              src="../static/icon_map_current.png"
            ></image>
            <text class="address">{{ userAddressName }}</text>
          </view>
          <view class="refreshUserAddress" @click="refreshUserAddress">
            <image
              style="width: 38rpx; height: 38rpx"
              src="/static/image/icon/reload.png"
              mode=""
            ></image
            ><text class="dw">刷新定位</text>
          </view>
        </view>
      </view>
      <view class="bottom-menu-panel">
        <view class="visit">
          <view class="cont">
            <image
              class="icon"
              src="../static/iocn_positioning.png"
              mode=""
            ></image>
            <text class="desc">拜访打卡</text>
          </view>
        </view>

        <view class="sign-record" @tap="triggerJumpToSignRecord()">
          <view class="cont">
            <image class="icon" src="../static/icon_record.png" mode=""></image>
            <text class="desc">打卡记录</text>
          </view>
        </view>
        <view
          class="sign-button"
          :class="{
            isUnableToCheckIn: !isSignIn || siginType == 3 || abnormalClient,
            sign: siginType === 1 && isSignIn,
            signout: siginType === 2 && isSignIn,
          }"
          @tap="throttleedLoadMore"
        >
          <template v-if="siginType == 1 || siginType == 2">
            <text>{{
              siginType === 1 ? "签到" : visitType == "3" ? "已签到" : "签退"
            }}</text>
            <text>{{ nowTime }}</text>
            <text
              class="inStoreTime"
              v-if="inStoreTime && siginType === 2 && visitType !== '3'"
              >您已在场{{ inStoreTime }}分钟</text
            >
          </template>
          <template v-if="siginType == 3">
            <text>今日已拜访</text>
            <text class="inStoreTime" style="color: #ffffff"
              >去打卡其他客户吧</text
            >
          </template>
        </view>
      </view>
    </view>
    <p-tabbar tabbarSelect="work"></p-tabbar>
    <uv-picker
      ref="picker1"
      :columns="pickerColumns"
      keyName="name"
      :closeOnClickOverlay="false"
      color="#9CA2A9"
      activeColor="#000"
      @confirm="pickerConfirm"
    ></uv-picker>
    <uv-popup ref="popupRef" mode="bottom" round="16rpx">
      <view style="width: 100%" class="isOutOfRangeClockIn-panel">
        <view class="title">
          <text @tap="superableCancel">取消</text>
          <text>超范围打卡</text>
          <text @tap="superableSure" class="sure">确定</text>
        </view>
        <view class="text-panel">
          <uv-textarea
            v-model="superableResult"
            placeholder="请填写超范围打卡理由"
            adjustPosition
            :maxlength="200"
          ></uv-textarea>
        </view>
      </view>
    </uv-popup>

    <uv-popup
      ref="popupReport"
      mode="center"
      :safeAreaInsetBottom="false"
      round="16rpx"
    >
      <view>
        <view class="popupReport-panel">
          <view class="colse-icon">
            <uv-icon
              name="close"
              size="32rpx"
              @tap="popupReportCancel"
              color="#1D1D1D"
            ></uv-icon>
          </view>
          <view class="title">
            <text>上报正确地址</text>
          </view>
          <view class="user-address">
            <text class="address">{{ userAddressName }}</text>
            <view class="refresh-btn" @tap="refreshUserAddress">
              <uv-icon name="reload" size="32rpx" color="#fff"></uv-icon>
              <text>刷新位置</text>
            </view>
          </view>
          <view class="uploadImage-panel">
            <view class="uploadImage-title">拍照上传</view>
            <view class="content">
              <template v-if="reportFileList && reportFileList.length">
                <view
                  class="item"
                  v-for="(item, index) in reportFileList"
                  :key="index"
                >
                  <image :src="item.fileUrl" mode="" class="imgs"></image>
                  <image
                    @tap="deteleFileList"
                    :data-value="item"
                    src="../static/icon_item_close.png"
                    class="delete-icon"
                  ></image>
                </view>
              </template>
              <view class="item none" @tap="addReportFile" v-else>
                <image
                  class="icon"
                  src="../static/icon_camera.png"
                  mode=""
                ></image>
                <text>添加图片</text>
              </view>
            </view>
          </view>
          <view class="remark-panel">
            <view class="remark-title">申请说明</view>
            <view class="content">
              <uv-textarea
                v-model="reportRemarkValue"
                placeholder="请填写重新标注理由"
                border="none"
              ></uv-textarea>
            </view>
          </view>
          <view class="sumbit-buttom" @tap="sumbitReport">提交</view>
        </view>
      </view>
    </uv-popup>
    <view
      class="rizhi"
      v-if="visitType === '3' && isEvaluate != 2"
      @click="triggerRec"
    >
      <image
        style="width: 320rpx; height: 80rpx"
        src="/static/image/icon/rizhixie.png"
        mode=""
      ></image>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { onLoad, onUnload, onShow } from "@dcloudio/uni-app";
import {
  baiduToGaoDeFun,
  throttle,
  formatTimestamp,
  buildQuery,
  isKAManager,
  imageCompress,
  debounce,
} from "/utils/utils.js";
import { fetchUserLocation } from "/utils/bMapUtils.js";
import {
  USER_INFO_KEY,
  SAVE_SIGN_INFO_PARAMS_KEY,
  SIGN_OUT_INFO_KEY,
  CLIENT_INFO_PARAMS_KEY,
} from "/common/const/cache.js";
import {
  getByCurrentTime,
  checkOtcSignIn,
  saveSignIn,
  saveOtcAccountAddress,
  getPunchSetById,
  terminalproduct,
  signInfo,
  checkOtcAccountAddress,
} from "/common/api/sign/index.js";
import {
  getClientInfoById,
  dictionaryDetail,
  updateBusiness,
} from "/common/api/business/index.js";
import { uploadPic } from "/common/api/file/index.js";

import { useUserStore } from "@/common/store/user";
import { useVisitStore } from "@/common/store/visit";
const userStore = useUserStore();
const visitStore = useVisitStore();
const isScene = ref(true); //是否在场
const isAgent = ref(true); //是否在场
const onChange = (e) => {
  isScene.value = !isScene.value;
  // visitStore.setScene(isScene.value);
};
const onAgentChange = (e) => {
  isAgent.value = !isAgent.value;
  // visitStore.setAgent(isAgent.value);
};
const isManage = () => {
  return UserInfo.posts[0].code == "10002";
};
const isEvaluate = ref(2); //是否评价
const visitType = ref(visitStore.$state.visitType); //拜访类型
const firstTitle = ref("");
const agentTitle = ref("代理商");
const clientDoctor = ref("");
const agentShang = ref("");
const zsdbName = computed(() => {
  return visitStore.$state.agentEmployee?.personName;
}); //招商代表姓名
const zsdbPost = computed(() => {
  return visitStore.$state.agentEmployee?.departNames;
}); //招商代表职位
const clientShop = computed(() => {
  return visitStore.$state.clientShop;
});

const showUpload = ref(false);
const addressCheck = ref("");
const accountName = ref("");
const addressSetOne = (id) => {
  const arr = clientShop.value.locationList.filter((item) => item.id === id);
  getShowUpload(arr[0]?.remark);
  abnormalClient.value = false;
  if (!arr[0].address) {
    abnormalClient.value = true;
  }
  console.log("地址数据:", arr, abnormalClient.value);
  visitStore.setClientShop({
    ...clientShop.value,
    address: arr[0].address,
    latitude: arr[0].latitude,
    longitude: arr[0].longitude,
  });
  // 形容一下
  checkOtcSignInFun();
};
let isProcessing = false; // 加锁变量
const addressChange = (e) => {
  if (isProcessing) return; // 如果正在处理中，则直接返回，防止重复触发

  isProcessing = true; // 开始处理，锁定

  try {
    addressCheck.value = e.detail;
    visitStore.setAddressCheck(addressCheck.value);
    addressSetOne(addressCheck.value);
  } finally {
    isProcessing = false; // 无论是否出错，最终解锁
  }
};
const getShowUpload = (accountName) => {
  showUpload.value = false;
  checkOtcAccountAddress({
    accountId: clientShop.value.id,
    currentTime: currentTime.value,
    accountName,
  }).then((res) => {
    showUpload.value = res.data;
  });
};
const throttleAddressChange = debounce(addressChange, 250);
// watch(
//   () => visitStore.$state.clientDoctor,
//   () => {
//     console.log(visitStore.$state.clientDoctor, "watchDoctor");
//     // if (visitStore.$state.clientDoctor?.id) {
//     //   getShowUpload();
//     // }
//     // if (
//     //   !visitStore.$state.clientDoctor?.id ||
//     //   visitStore.$state.clientDoctor?.init
//     // ) {
//     //   return;
//     // }
//     triggerClientChange();
//   }
// );

// 用户信息
const UserInfo = uni.getStorageSync(USER_INFO_KEY); // 从缓存里拿用户信息

const salesmanId = UserInfo && UserInfo.id ? UserInfo.id : null; // 业务员 ID，确保不存在时为 null
const personCode = UserInfo && UserInfo.code ? UserInfo.code : null; //	edp编码
const personName = UserInfo && UserInfo.name ? UserInfo.name : null; //打卡人姓名

const loadingPage = ref(false); //页面加载loading

const siginType = ref(visitStore.$state.siginType || 1); //当前是签到还是签退 打卡类型(1=签到 2=签退)
const superableResult = ref("");
const superableScope = ref(true); //是否可以超范围打卡
const isSignIn = ref(true); //是否支持打卡
const offset = ref(true); //是否支持打卡
const offsetValue = ref(0); //是否支持打卡

const inStoreTime = ref("");

const nowTime = ref("");

// 用户当前地理位置的经纬度 百度地图
const latitude = ref(null);
const longitude = ref(null);

const latitudeBaidu = ref(null);
const longitudeBaidu = ref(null);
const userAddressName = ref("");

const markers = ref([]); //我的位置mark点

const selectVisit = ref(null); //拜访类型
const assistVisitTarget = ref(null); //协访对象
const selectClientInfo = ref(null); //拜访客户
const abnormalClient = ref(false); //是否是异常客户

const currentTime = ref("");
let pickerColumns = [];

// ref
const popupComponent = ref(null); //弹窗组件
const picker1 = ref(null); //弹窗组件
const popupRef = ref(null); //弹窗组件
const popupReport = ref(null); //上报弹窗组件

const reportRemarkValue = ref("");
const reportFileList = ref([]);

// 定时器变量
let timer = null;
let inStoreTimer = null;
let setNowLocationTimer = null;
// //当前地理位置是否支持打卡
// 以及 后台配置是否允许超范围打卡 是否超范围打卡(1=是 2=否)
const checkOtcSignInFun = () => {
  if (visitStore.$state.visitType == "3") {
    superableScope.value = true;
    isSignIn.value = true;
    return;
  }
  if (
    !visitStore.$state.clientShop?.latitude ||
    !latitudeBaidu.value ||
    !longitudeBaidu.value
  ) {
    superableScope.value = false;
    isSignIn.value = false;
    return;
  }
  let parmas = {
    purMerchantLatitude: visitStore.$state.clientShop?.latitude || "",
    purMerchantLongitude: visitStore.$state.clientShop?.longitude || "",
    latitude: latitudeBaidu.value,
    longitude: longitudeBaidu.value,
  };
  checkOtcSignIn(parmas).then((res) => {
    const { code, data } = res;
    console.log(data, "-----data------------------------");
    if (code == 0) {
      superableScope.value = data.superableScope == 1; // 是否超范围打卡(1=是 0=否)
      isSignIn.value = data.isSignIn === "YES";
      offsetValue.value = data.offset;
    }
  });
};
const triggerAgentChange = () => {
  uni.navigateTo({
    url: "/pages/subVisit/selectAgent/selectAgent",
  });
};
const triggerDoctor = () => {
  // if (siginType.value != 1) {
  //   return;
  // }
  visitStore.setCopyObj({
    clientShop: visitStore.$state.clientShop,
    clientDept: visitStore.$state.clientDept,
    clientDoctor: visitStore.$state.clientDoctor,
    agentEmployee: visitStore.$state.agentEmployee,
  });
  if (visitStore.$state.visitType == "1") {
    uni.navigateTo({
      url: "/pages/subVisit/selectClientShop/selectClientShop",
    });
  }
  if (visitStore.$state.visitType == "2") {
    uni.navigateTo({
      url: "/pages/subVisit/selectSub/selectSub",
    });
  }
};
onLoad((option) => {
  // visitStore.setClientDoctor(null);
  // visitStore.setClientShop(null);
  // visitStore.setClientDoctor(null);
  // visitStore.setClientDept(null);
  visitStore.setLocation(null);
  let pageTitle = "";
  switch (visitStore.$state.visitType) {
    case "1":
      pageTitle = "终端拜访";
      firstTitle.value = "拜访客户";
      break;
    case "2":
      pageTitle = "市场协访";
      firstTitle.value = "协访客户";
      break;
    case "3":
      pageTitle = "代理商拜访";
      firstTitle.value = "";
      agentTitle.value = "拜访对象";
      break;
  }
  // 设置页面标题;
  uni.setNavigationBarTitle({
    title: pageTitle,
  });

  const timestamp = Date.now();
  currentTime.value = formatTimestamp(timestamp);
  // 初始化拜访类型
  // initSelectGroups();
  // 获取用户当前经纬度
  initUserLocal(true);

  // 开启应用进入前台时接收位置消息。
  // startLocationUpdate();
  setNowLocationTimer = setInterval(() => {
    console.log("10s更新一次地理位置");
    initUserLocal();
  }, 20000);

  // 初始化当前的时间
  updateCurrentTime(); // 初始化显示当前时间
  timer = setInterval(updateCurrentTime, 1000); // 每分钟更新一次
});
onUnload(() => {
  uni.$off("selectClientInfo");
  uni.$off("assistVisitMembers");
  if (timer) clearInterval(timer);
  if (inStoreTimer) clearInterval(inStoreTimer);
  if (setNowLocationTimer) clearInterval(setNowLocationTimer);
});
onShow(() => {
  console.log(visitStore.$state, "----------------------visitStore");
  if (visitStore.$state.visitType == "3") {
    visitStore.setClientDept(null);
    visitStore.setClientDoctor(null);
    visitStore.setClientShop(null);
    agentShang.value = `${visitStore.$state.agent?.agentName}/${visitStore.$state.agent?.agentEmployeeName}`;
    isEvaluate.value = visitStore.$state.agentEmployee?.isEvaluate;
  } else {
    if (!visitStore.$state.clientDept || !visitStore.$state.clientDoctor) {
      visitStore.setClientShop(visitStore.$state.copyObj.clientShop);
      visitStore.setClientDept(visitStore.$state.copyObj.clientDept);
      visitStore.setClientDoctor(visitStore.$state.copyObj.clientDoctor);
      visitStore.setAgentEmployee(visitStore.$state.copyObj.agentEmployee);
    }
    clientDoctor.value = `${visitStore.$state.clientShop?.name}/${visitStore.$state.clientDept?.departmentName}/${visitStore.$state.clientDoctor?.name}`;
    agentShang.value = visitStore.$state.agent?.agentEmployeeName;
  }
  if (visitStore.$state.needRefresh) {
    if (
      visitStore.$state.clientShop?.locationList &&
      visitStore.$state.clientShop?.locationList.length > 0
    ) {
      addressCheck.value =
        visitStore.$state.addressCheck ||
        visitStore.$state.clientShop?.locationList[0]?.id;
      addressSetOne(addressCheck.value);
    }
    isAgent.value = visitStore.$state.agent?.isAgent == 0 ? false : true;
    isScene.value =
      visitStore.$state.agentEmployee?.isScene == 0 ? false : true;
    // calculateInStoreTime(visitStore.$state.clientShop?.createDate); // 计算在场时间
    // triggerClientChange();
  }
});
// 小程序类目没有这个接口的权限，所以注释了
// const startLocationUpdate = () => {
// 	uni.startLocationUpdate({
// 		success: (res) => {
// 			console.log('开启应用接收位置消息成功');
// 			initUserLocal();
// 		},
// 		fail: (err) => {
// 			console.error('开启应用接收位置消息失败：', err);
// 		},
// 		complete: (msg) => console.log('调用开启应用接收位置消息 API 完成')
// 	});
// };

const handleUploadLocation = async (item) => {
  visitStore.setNeedRefresh(false);
  if (item) accountName.value = item.remark;
  popupReport.value.open();
};

// 获取拜访类型的字典值
const initSelectGroups = () => {
  dictionaryDetail("1871819555403079682").then((res) => {
    const { code, data } = res;
    if (code == 0) {
      pickerColumns = [data];
    }
  });
};

// 超范围打卡弹窗确定
const superableSure = () => {
  popupRef.value.close();
  if (superableResult.value) {
    saveSignInFun();
  }
};
// 超范围打卡弹窗取消
const superableCancel = () => {
  popupRef.value.close();
  superableResult.value = "";
};

const saveSignInFun = async () => {
  // if (!visitStore.$state.clientDoctor) {
  //   uni.showToast({
  //     title: "请选择拜访客户",
  //     icon: "none",
  //     duration: 2000,
  //   });
  //   return;
  // }
  // 如果是拜访 没有选择拜访类型
  // if (!selectVisit.value && visitType.value === "visit") {
  //   uni.showToast({
  //     title: "请先选择拜访类型",
  //     icon: "none",
  //     duration: 2000,
  //   });
  //   return;
  // }
  // xin
  if (siginType.value != 1 && visitType.value == "3") {
    return;
  }
  if (
    !(
      latitude.value &&
      longitude.value &&
      latitudeBaidu.value &&
      longitudeBaidu.value
    )
  ) {
    uni.showToast({
      title: "获取当前位置经纬度失败",
      icon: "none",
    });
    return;
  }
  if (siginType.value == 3) {
    return;
  }
  if (!visitStore.clientShop?.address && visitType.value != "3") {
    uni.showToast({
      title: "请先上报客户经纬度",
      icon: "none",
      duration: 2000,
    });
    return;
  }

  if (!isSignIn.value) {
    uni.showToast({
      title: "当前无法打卡!",
      icon: "none",
      duration: 2000,
    });
    return;
  }
  if (
    (visitType.value == "1" || visitType.value == "2") &&
    isAgent.value &&
    !agentShang.value
  ) {
    uni.showToast({
      title: "代理商在场请选择代理商",
      icon: "none",
      duration: 2000,
    });
    return;
  }
  if (
    superableScope.value &&
    !superableResult.value &&
    visitType.value != "3"
  ) {
    popupRef.value.open();
    return;
  }

  if (!latitudeBaidu.value || !longitudeBaidu.value) {
    uni.showToast({
      title: "登录用户经纬度不存在",
      icon: "none",
      duration: 2000,
    });
    return;
  }
  if (!visitStore.$state.clientShop?.address && visitType.value != "3") {
    uni.showToast({
      title: "选中的客户位置信息不存在",
      icon: "none",
      duration: 2000,
    });
    return;
  }
  console.log(visitStore.$state, "----------------------visitStore");
  let parmas = {
    address: userAddressName.value,
    // agentEmployeeId: visitStore.$state.clientShop.agentId,
    // agentEmployeeName: visitStore.$state.clientShop.agentName,
    baseHospitalId: visitStore.$state.clientDept?.departmentId || null,
    baseHospitalName: visitStore.$state.clientDept?.departmentName || null,
    category: 2,
    currentTime: currentTime.value,
    destination: visitStore.$state.clientShop?.address || null,
    doctorId: visitStore.$state.clientDoctor?.id || null,
    doctorName: visitStore.$state.clientDoctor?.name || null,
    durationTime: inStoreTime.value,
    exceptionReason: superableResult.value ? 1 : 0, //	异常原因(0=正常 1=超范围打卡 2=未打卡 3=迟到打卡 4=早退)
    exceptionStatus: superableResult.value ? 2 : 1, //	SignExceptionStatusEnum：#是否为异常记录（异常：2,正常：1,默认：1）
    // todo
    imageIds: null,
    latitude: latitudeBaidu.value + "",
    longitude: longitudeBaidu.value + "",
    offset: offsetValue.value, //打卡偏差
    personCode: personCode,
    personId: salesmanId,
    personName: personName,
    purMerchantId: visitStore.$state.clientShop?.id || null,
    purMerchantLatitude: visitStore.$state.clientShop?.latitude || null,
    purMerchantLongitude: visitStore.$state.clientShop?.longitude || null,
    purMerchantName: visitStore.$state.clientShop?.name || null,
    // todo
    remarks: null,
    siginType: siginType.value, //打卡类型(1=签到 2=签退)
    superableScopeRemarks: superableResult.value,
    //todo
    visitPurpose: null,
    //todo
    visitSumup: null,
    visitType: visitType.value, //	VisitTypeEnum：#拜访类型（终端：1,市场：2,代理商：3）
  };

  switch (visitType.value) {
    case "1":
      if (isAgent.value) {
        parmas.agentEmployeeId = visitStore.$state.agent.agentEmployeeId;
        parmas.agentEmployeeName = visitStore.$state.agent.agentEmployeeName;
        parmas.agentId = visitStore.$state.agent.agentId;
        parmas.agentName = visitStore.$state.agent.agentName;
        parmas.agentIsPresent = 1;
      } else {
        parmas.agentIsPresent = 0;
      }
      break;
    case "2":
      if (isAgent.value) {
        parmas.agentEmployeeId = visitStore.$state.agent.agentEmployeeId;
        parmas.agentEmployeeName = visitStore.$state.agent.agentEmployeeName;
        parmas.agentId = visitStore.$state.agent.agentId;
        parmas.agentName = visitStore.$state.agent.agentName;
        parmas.agentIsPresent = 1;
      } else {
        parmas.agentIsPresent = 0;
      }
      parmas.coachedErpCode = visitStore.$state.agentEmployee.personCode;
      parmas.coachedPersonId = visitStore.$state.agentEmployee.personId;
      parmas.coachedPersonName = visitStore.$state.agentEmployee.personName;
      if (isScene.value) {
        // 少了个岗位
        parmas.crmIsPresent = 1;
      } else {
        parmas.crmIsPresent = 0;
      }
      break;
    case "3":
      parmas.agentEmployeeId = visitStore.$state.agent.agentEmployeeId;
      parmas.agentEmployeeName = visitStore.$state.agent.agentEmployeeName;
      parmas.agentId = visitStore.$state.agent.agentId;
      parmas.agentName = visitStore.$state.agent.agentName;
      break;
  }
  if (siginType.value == 1) {
    saveSignIn(parmas).then((res) => {
      if (res.code == 0) {
        uni.showToast({
          title: "签到成功",
          icon: "none",
        });
        superableResult.value = "";
        siginType.value = 2; //签退模式
        calculateInStoreTime();
        if (visitType.value == "3") {
          visitStore.setAgentEmployee({
            ...visitStore.$state.agentEmployee,
            id: res.data.id,
            personCode: res.data.coachedErpCode,
          });
          setTimeout(() => {
            uni.navigateTo({
              url: `/pages/subVisit/visitLog/visitLog`,
            });
          }, 500);
        }
      } else {
        uni.showToast({
          title: "签到失败",
          icon: "none",
        });
      }
    });
  } else {
    // 拜访需要写拜访日志
    visitStore.setSignoutParams(parmas);
    if (visitType.value == "2") {
      // visitStore.setNeedRefresh(true);
      //协访直接签退后填写协访评价
      // saveSignIn(parmas).then((res) => {
      //   if (res.code == 0) {
      //     uni.showToast({
      //       title: '签退成功',
      //       icon: 'none',
      //       duration: 2000
      //     });
      //     setTimeout(() => {
      //       uni.setStorageSync(SIGN_OUT_INFO_KEY, res.data);
      //       uni.redirectTo({
      //         url: '/pages/subVisit/comment/comment'
      //       });
      //     }, 1000);
      //   } else {
      //     uni.showToast({
      //       title: '签退失败',
      //       icon: 'none'
      //     });
      //   }
      // });
      saveSignIn(parmas).then((res) => {
        if (res.code == 0) {
          uni.showToast({
            title: "签退成功",
            icon: "none",
            duration: 2000,
          });
          setTimeout(() => {
            uni.redirectTo({
              url: "/pages/subVisit/comment/comment" + `?id=${res.data.id}`,
            });
          }, 500);
        } else {
          uni.showToast({
            title: "签退失败",
            icon: "none",
          });
        }
      });
    }
    if (visitType.value == "1") {
      setTimeout(() => {
        uni.redirectTo({
          url: "/pages/subVisit/visitLog/visitLog",
        });
      }, 500);
    }
    // else {
    //   uni.setStorageSync(CLIENT_INFO_PARAMS_KEY, selectClientInfo.value);
    //   uni.setStorage({
    //     key: SAVE_SIGN_INFO_PARAMS_KEY,
    //     data: parmas,
    //     success: () => {
    //       uni.redirectTo({
    //         url: `/pages/subVisit/visitLog/visitLog?pageType=${visitType.value}`,
    //       });
    //     },
    //   });
    // }
  }
};
const throttleedLoadMore = throttle(saveSignInFun, 2500);

const calculateInStoreTime = (startTime) => {
  console.log(startTime, "开始时间");
  let start = startTime ? new Date(startTime) : new Date();

  if (isNaN(start.getTime())) {
    throw new Error("Invalid start time");
  }

  const updateInStoreTime = () => {
    const now = new Date();
    const diffInMinutes = Math.floor((now - start) / (60 * 1000)); // 计算差值，单位为分钟
    inStoreTime.value = diffInMinutes; // 更新响应式变量为纯分钟数
  };

  // 初始化时立即更新一次
  updateInStoreTime();

  // 每分钟更新一次
  inStoreTimer = setInterval(() => {
    updateInStoreTime();
  }, 60 * 1000);
};

// 更新时间的函数
const updateCurrentTime = () => {
  const now = new Date();
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  nowTime.value = `${hours}:${minutes}`;
};

// 获取最近签到过的客户列表
const getRecentSignedCustomer = async () => {
  const result = await getByCurrentTimeFun();
  loadingPage.value = false;
  const { code, data } = result;
  if (code == 0 && data) {
    if (visitType.value == "visit") {
      selectVisit.value =
        pickerColumns[0].find((item) => item.value == data.category) || null;
    } else {
      visitStore.setClientShop({
        id: data.purMerchantId,
        name: data.purMerchantName,
        address: data.destination,
        latitude: data.purMerchantLatitude,
        longitude: data.purMerchantLongitude,
        userId: data.coachedPersonId,
        userName: data.coachedPersonName,
        agentId: data.agentEmployeeId,
        agentName: data.agentEmployeeName,
        locationList: null,
      });
      visitStore.setClientDept({
        departmentId: data.baseHospitalId,
        departmentName: data.baseHospitalName,
      });
      visitStore.setClientDoctor({
        ...visitStore.$state.clientDoctor,
        name: data.doctorName,
        id: data.doctorId,
        init: true,
      });
      visitStore.setAgent({
        agentEmployeeId: data.agentEmployeeId,
        agentEmployeeName: data.agentEmployeeName,
        agentId: data.agentId,
        agentName: data.agentName,
        isAgent: data.agentIsPresent,
      });
      visitStore.setAgentEmployee({
        personName: data.coachedPersonName,
        departNames: data.departNames,
        isScene: data.crmIsPresent,
        personId: data.coachedPersonId,
        id: data.id,
        personCode: data.coachedErpCode,
      });
      console.log(visitStore.$state.clientDoctor, "-----clientDoctor");
      isAgent.value = data?.agentIsPresent == 0 ? false : true;
      isScene.value = data?.crmIsPresent == 0 ? false : true;
      if (visitStore.$state.visitType == "3") {
        agentShang.value = `${data?.agentName}/${data?.agentEmployeeName}`;
        isEvaluate.value = data?.isEvaluate;
      } else {
        // clientDoctor.value = `${visitStore.$state.clientShop?.name}/${visitStore.$state.clientDept?.departmentName}/${visitStore.$state.clientDoctor?.name}`;
        agentShang.value = data?.agentEmployeeName;
      }
    }
    if (data.siginType == 2) {
      siginType.value = 3;
      if (inStoreTimer) clearInterval(inStoreTimer);
    } else {
      calculateInStoreTime(data.createDate);
      siginType.value = 2;
      if (visitStore.$state.visitType == "3") {
        if (data?.siginType == 1) {
          if (data?.isEvaluate == 1) {
            siginType.value = 2;
          } else if (data?.isEvaluate == 2) {
            siginType.value = 3;
          } else {
            siginType.value = 1;
          }
        }
      }
      checkOtcSignInFun();
    }
  } else {
    //如果不存在
    isEvaluate.value = 2;
    siginType.value = 1;
    checkOtcSignInFun();
  }
};
const triggerClientChange = async () => {
  const id = clientShop.value.id;
  // 异常用户
  abnormalClient.value = false;
  if (!visitStore.clientShop.address) {
    abnormalClient.value = true;
  }

  if (
    !clientShop.value.latitude ||
    !(
      latitude.value &&
      longitude.value &&
      latitudeBaidu.value &&
      longitudeBaidu.value
    )
  ) {
    superableScope.value = false;
    isSignIn.value = false;
    return;
  }
  // 判断当前是否满足打卡条件
  checkOtcSignInFun();
  // 获取当前商户的签到状态
  const result = await getByCurrentTimeFun(id);
  const { code, data } = result;
  if (code == 0 && data) {
    if (visitType.value == "visit") {
      assistVisitTarget.value = {
        name: data.coachedPersonName,
        userId: data.coachedPersonId,
      };
    }
    if (data.siginType == 2) {
      siginType.value = 3;
      if (inStoreTimer) clearInterval(inStoreTimer);
    } else {
      // selectVisit.value =
      //   pickerColumns[0].find((item) => item.value == data.category) || null;
      siginType.value = 2;
      calculateInStoreTime(data.createDate);
    }
  } else {
    siginType.value = 1;
  }
};
const triggerRec = () => {
  uni.navigateTo({
    url: "/pages/subVisit/visitLog/visitLog",
  });
};
const getByCurrentTimeFun = (purMerchantId) => {
  let parmas = {
    createDate: currentTime.value,
    personId: salesmanId,
    visitType: visitStore.$state.visitType,
    doctorId: visitStore.$state.clientDoctor?.id,
    agentEmployeeId: visitStore.$state.agent?.agentEmployeeId,
  };
  if (visitType != "3") {
    parmas.purMerchantId = visitStore.$state.clientShop?.id || "";
  }
  return getByCurrentTime(parmas)
    .then((Result) => {
      return Result; // 成功时返回结果
    })
    .catch((error) => {
      console.error("Error:", error);
      throw error; // 将错误抛出，供外部捕获
    });
};
const initUserLocal = (isInit) => {
  // 调用封装的百度获取经纬度的方法
  fetchUserLocation(
    ({ baidu, gaode, address }) => {
      console.log(baidu, gaode, address, "刷新用户经纬度");

      latitudeBaidu.value = baidu.latitude; // 给经纬度赋值
      longitudeBaidu.value = baidu.longitude; // 给经纬度赋值

      latitude.value = gaode.latitude; // 获取纬度
      longitude.value = gaode.longitude; // 获取经度

      visitStore.setLocation({
        latitude: baidu.latitude,
        longitude: baidu.longitude,
      });

      userAddressName.value = address;
      // if (isInit) {
      //   // 获取最近的签到没有签退的客户
      //   checkOtcSignInFun();
      // }
      if (isInit && !visitStore.$state.isIndex) {
        // 获取最近的签到没有签退的客户
        getRecentSignedCustomer();
      }
      if (isInit && visitStore.$state.isIndex) {
        // 获取最近的签到没有签退的客户
        checkOtcSignInFun();
      }
    },
    (error) => {
      loadingPage.value = false;
      uni.showToast({
        title: "获取当前位置经纬度失败",
        icon: "none",
      });
      // 错误处理
      console.error("位置获取失败:", error);
    }
  );
};

const pickerConfirm = (arr) => {
  selectVisit.value = arr.value[0];
};
// 打开选择弹窗
const triggerVisitType = () => {
  picker1.value.open();
};

const triggerClient = () => {
  if (visitType.value == "assistDefense" && !assistVisitTarget.value) {
    uni.showToast({
      title: "请先选择协访对象",
      icon: "none",
    });
    return;
  }
  const queryParams = {
    latitude: latitudeBaidu.value || "",
    longitude: longitudeBaidu.value || "",
    pageType: visitType.value,
  };
  // 如果是协访，则追加 `id` 参数
  if (visitType.value === "assistDefense" && assistVisitTarget.value?.userId) {
    queryParams.id = assistVisitTarget.value.userId;
  }
  //拜访客户
  uni.navigateTo({
    url: `/subPackage/selectCustomerPage/index?${buildQuery(queryParams)}`,
  });
};

const triggerJumpToSignRecord = () => {
  visitStore.setActiveTab(1);
  uni.navigateTo({
    url: "/pages/subVisit/signRecord/signRecord",
  });
};

// ----------------上报弹窗的方法
const triggerReport = () => {
  popupReport.value.open();
};
const popupReportCancel = () => {
  popupReport.value.close();
  reportFileList.value = [];
  reportRemarkValue.value = "";
};
const refreshUserAddress = () => {
  uni.showLoading({
    title: "更新位置中",
  });
  fetchUserLocation(
    ({ baidu, gaode, address }) => {
      uni.showLoading({
        title: "更新成功！",
      });
      console.log(baidu, gaode, address, "刷新用户经纬度");

      latitudeBaidu.value = baidu.latitude; // 给经纬度赋值
      longitudeBaidu.value = baidu.longitude; // 给经纬度赋值
      visitStore.setLocation({
        latitude: baidu.latitude,
        longitude: baidu.longitude,
      });

      latitude.value = gaode.latitude; // 获取纬度
      longitude.value = gaode.longitude; // 获取经度
      userAddressName.value = address;

      setTimeout(() => {
        uni.hideLoading();
      }, 500);
    },
    (error) => {
      uni.showLoading({
        title: "位置获取失败！",
      });
      setTimeout(() => {
        uni.hideLoading();
      }, 1000);
      // 错误处理
      console.error("位置获取失败:", error);
    }
  );
};
// 提交上报
const sumbitReport = () => {
  if (reportFileList.value.length) {
    uni.showLoading({
      title: "更新客户数据中，请稍后",
    });
    const idString = reportFileList.value.join(",");
    let parmas = {
      accountId: clientShop.value.id,
      imageIds: idString,
      latitude: latitudeBaidu.value + "",
      longitude: longitudeBaidu.value + "",
      registerAddress: userAddressName.value,
      remark: reportRemarkValue.value,
      currentTime: currentTime.value,
    };
    if (accountName.value) {
      parmas.accountName = accountName.value;
    } else {
      parmas.accountName = visitStore.$state.clientShop.name;
    }
    console.log(parmas, "---parmas");
    saveOtcAccountAddress(parmas).then((res) => {
      if (res.code == 0) {
        // 更新信息
        clientShop.value.locationList.forEach((item) => {
          if (item.id == addressCheck.value) {
            item.address = userAddressName.value;
            item.latitude = latitudeBaidu.value;
            item.longitude = longitudeBaidu.value;
          }
        });
        visitStore.setClientShop({
          ...clientShop.value,
          address: userAddressName.value,
          latitude: latitudeBaidu.value,
          longitude: longitudeBaidu.value,
          distance: 0,
        });
        getShowUpload(parmas.accountName);
        triggerClientChange();

        uni.showLoading({
          title: "更新成功！",
        });
        setTimeout(() => {
          uni.hideLoading();
          popupReportCancel();
        }, 1000);
      }
    });
  } else {
    uni.showToast({
      title: "请先拍照上传图片",
      icon: "none",
    });
  }
};
// 删除图片
const deteleFileList = (event) => {
  reportFileList.value = [];
};
// 上传图片
const addReportFile = () => {
  if (!reportFileList.value.length) {
    wx.chooseMedia({
      count: 1,
      mediaType: ["image"],
      sizeType: ["compressed"], //可以指定是原图还是压缩图，默认二者都有
      sourceType: ["camera"],
      camera: "back",
      success: async (res) => {
        uni.showLoading({
          title: "图片上传中",
        });
        const tempFilePaths = res.tempFiles[0].tempFilePath;
        // const size = res.tempFiles[0].size;
        imageCompress(tempFilePaths).then((imageCompressRes) => {
          uploadPic(imageCompressRes).then((res) => {
            console.log("图片接口返回结果", res);
            const result = JSON.parse(res);
            const { code, data, msg } = result;
            if (code == 0) {
              uni.hideLoading();
              reportFileList.value = data || [];
            } else {
              uni.showLoading({
                title: msg,
              });
              setTimeout(() => {
                uni.hideLoading();
              }, 2000);
            }
          });
        });
        console.log(tempFilePaths, "---tempFilePaths");
        // console.log(image, 'image');
      },
      fail(res) {
        console.log("失败", res);
      },
    });
  }
};
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";
.address-line {
  display: flex;
  font-size: 20rpx;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
  &-left {
    display: flex;
    align-items: center;
  }
  .address {
    color: #8d9094;
  }
}
.rizhi {
  position: fixed;
  bottom: 31vh;
  left: 28vw;
}
.refreshUserAddress {
  width: 140rpx;
  height: 48rpx;
  position: absolute;
  display: flex;
  align-items: center;
  top: 24rpx;
  right: 24rpx;
  .dw {
    flex: 1;
    color: #6adb60;

    /* 点文本-常规/12pt regular */
    font-family: "PingFang SC";
    font-size: 24rpx;
    font-style: normal;
  }
}
.doctorName {
  display: flex;
  justify-content: space-between;
}
.agentPlace {
  color: var(---Gray6, #8d9094);

  /* 点文本-加粗/14pt bold */
  font-family: "PingFang SC";
  font-size: 28rpx;
  font-style: normal;
  font-weight: 400;
}
.agentShang {
  color: var(---Gray7, #2f3133);
}
.scene {
  display: flex;
  justify-content: space-between;
  .scene-text {
    font-family: "PingFang SC";
    font-size: 28rpx;
    font-style: normal;
    font-weight: 400;
  }
  .tip {
    display: flex;
    padding: 0px 14rpx;
    justify-content: center;
    align-items: center;
    gap: 28rpx;
    border-radius: 40rpx;
    background: var(----, #fac8ca);
    color: var(----, #fc474c);

    /* 点文本-常规/10pt regular */
    font-family: "PingFang SC";
    font-size: 20rpx;
    font-style: normal;
    font-weight: 400;
  }
}
.createVisit-page {
  .content-panel {
    @include globalPageStyle();
    padding: 28rpx 20rpx;
    display: flex;
    flex-direction: column;
    .top-panel {
      overflow: hidden;
      @include cardBgCommonStyle();
      margin-bottom: 24rpx;
      .top-select-panel {
        display: flex;
        padding: 24rpx;
        border-bottom: 2rpx solid #ced4db;
        .right-desc {
          @include setlightFont(28rpx, 36rpx, #000);
        }
        .location_upload {
          text-align: right;
          font-size: 24rpx;
        }
        .right-address {
          color: var(---Gray6, #8d9094);

          /* 点文本-常规/12pt regular */
          font-family: "PingFang SC";
          font-size: 24rpx;
          font-style: normal;
          font-weight: 400;
        }
        .right-location {
          flex-shrink: 0;
          margin-left: 4rpx;
          font-size: 20rpx;
          margin-top: 4px;
          color: var(----, #fc474c);
          text-decoration-line: underline;
          text-decoration-style: solid;
          text-decoration-skip-ink: auto;
          text-decoration-thickness: auto;
          text-underline-offset: auto;
          text-underline-position: from-font;
        }
        .right-desc.right-location {
          font-size: 20rpx;
        }
        .icon-right {
          align-self: center;
        }
        .text-right {
          flex: 1;
        }
        .text-left {
          display: flex;
          &.more {
            margin: 24rpx 0 14rpx;
          }
          .right-title {
            width: 156rpx;
            text-align: right;
            @include setBoldFont(28rpx, 36rpx, $uni-text-color);
            font-weight: bold;
            &.require {
              &::before {
                content: "*";
                @include setBoldFont(28rpx, 36rpx, #f00);
              }
            }
          }
          .rigth-client-info {
            flex: 1;
            width: 70%;
            display: flex;
            flex-direction: column;
            .client-name {
              @include ellipsisBasic(1);
              margin-bottom: 22rpx;
              @include setBoldFont(28rpx, 36rpx, $uni-text-color);
            }
            .client-address {
              @include ellipsisBasic(1);
              @include setlightFont(22rpx, 28rpx, $uni-text-color-grey);
            }
            .client-address-err {
              @include setlightFont(22rpx, 28rpx, #d40404);
            }
            .client-rep {
              margin-top: 10rpx;
              @include setlightFont(22rpx, 28rpx, $uni-text-color-grey);
              text-align: right;
              text-decoration: underline;
            }
          }
        }
      }
    }
    .map-panel {
      flex: 1;
      @include cardBgCommonStyle();
      height: 0;
      overflow: hidden;
      position: relative;
      .map-content {
        width: 100%;
        height: 100%;
      }
      .obscuration {
        @include absoluteFull();
        border-radius: 16rpx;
        background: radial-gradient(
          45% 45% at center,
          rgba(217, 217, 217, 0) 0%,
          rgba(238, 238, 238, 0.54) 50.5%,
          #fff 100%
        );
        display: flex;
        justify-content: center;
        align-items: center;
        .content {
          position: relative;
          .img {
            width: 52rpx;
            height: 52rpx;
          }
          .address {
            width: 384rpx;
            overflow-wrap: break-word;
            white-space: pre-wrap;
            text-align: center;
            z-index: 15;
            @include setBoldFont(24rpx, 40rpx, #000);
            @include absoluteHorizontalCenter();
            top: 61rpx;
          }
        }
      }
    }
    .bottom-menu-panel {
      margin: 20rpx 0 28rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      .visit,
      .sign-record {
        text-align: center;
        width: 324.962rpx;
        height: 100rpx;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
        position: relative;
        .cont {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          padding-top: 10rpx;
        }
        &::before {
          z-index: 1;
          content: "";
          @include absoluteFull();
          background: url("../static/icon_subtract.png") no-repeat;
          background-size: cover;
        }
        .icon {
          z-index: 1;
          width: 48rpx;
          height: 48rpx;
          margin-bottom: 1rpx;
        }
        .desc {
          z-index: 1;
          @include setlightFont(24rpx, 40rpx, $uni-text-color-inverse);
        }
      }
      .visit {
        align-items: flex-start;
        padding-left: 84rpx;
      }
      .sign-record {
        align-items: flex-end;
        padding-right: 84rpx;
        &::before {
          transform: scaleX(-1);
        }
      }
      .sign-button {
        width: 210rpx;
        height: 210rpx;
        border-radius: 50%;
        background-color: #b7b7b7;
        border: 4rpx solid $uni-text-color-inverse;

        @include absoluteHorizontalCenter();
        z-index: 10;
        bottom: 6rpx;
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;

        box-sizing: border-box;
        border: 4rpx solid #fff;
        &.sign {
          @include setlightFont(28rpx, 32rpx, #fff);
          @include setBackgroundImage("../static/icon_checkin.png");
        }
        &.signout {
          @include setlightFont(28rpx, 32rpx, #276cac);
          @include setBackgroundImage("../static/icon_checkout.png");
        }
        .inStoreTime {
          @include setlightFont(20rpx, 26rpx, #276cac);
        }
        &.isUnableToCheckIn {
          @include setlightFont(28rpx, 32rpx, #fff);
          @include setBackgroundImage("../static/icon_uncheckin.png");
        }
      }
    }
  }
  .popupReport-panel {
    padding: 28rpx 12rpx 42rpx 30rpx;
    width: 594rpx;
    // height: 656rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    // align-items: center;
    justify-content: flex-start;
    position: relative;
    @include cardBgCommonStyle();
    .colse-icon {
      position: absolute;
      top: 24rpx;
      right: 12rpx;
    }
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      @include setBoldFont(28rpx, 44rpx, #1d1d1d);
      margin-bottom: 16rpx;
      width: 100%;
    }
    .user-address {
      width: 100%;
      box-sizing: border-box;
      padding-right: 24rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin-bottom: 56rpx;
      margin-bottom: 28rpx;
      @include setlightFont(24rpx, 32rpx, #bbbdbf);
      .address {
      }
      .refresh-btn {
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        background: $primary-gradient;
        height: 32rpx;
        @include setBoldFont(24rpx, 32rpx, #fff);
        margin-left: 20rpx;
        padding: 4rpx 6rpx;
        white-space: nowrap;
      }
    }
    .uploadImage-panel {
      margin-bottom: 28rpx;
      .uploadImage-title {
        margin-bottom: 10rpx;
        @include setBoldFont(28rpx, 44rpx, #1d1d1d);
        &::before {
          content: "*";
          @include setBoldFont(28rpx, 44rpx, #f00);
        }
      }
      .content {
        padding-right: 40rpx;
        .item {
          width: 200rpx;
          height: 200rpx;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border-radius: 16rpx;
          position: relative;
          overflow: hidden;
          &.none {
            height: 240rpx;
            width: 100%;
            background: #f1f1f1;
          }
          .imgs {
            width: 200rpx;
            height: 200rpx;
          }
          .delete-icon {
            width: 34rpx;
            height: 34rpx;
            position: absolute;
            right: 0;
            top: 0;
          }
          @include setlightFont(24rpx, 32rpx, rgba(29, 29, 29, 0.6));
          .icon {
            width: 48.292rpx;
            height: 39.428rpx;
          }
        }
      }
    }
    .remark-panel {
      .remark-title {
        margin-bottom: 8rpx;
        @include setBoldFont(28rpx, 44rpx, #1d1d1d);
      }
      .content {
        padding-right: 40rpx;
        height: 202rpx;
        :deep() {
          .uv-textarea {
            height: 100%;
            border-radius: 16rpx;
            background: #f1f1f1;
            box-sizing: border-box;
            .textarea-placeholder {
              @include setlightFont(24rpx, 32rpx, rgba(29, 29, 29, 0.6));
              @include setlightFont(24rpx, 32rpx, #1d1d1d);
            }
          }
        }
      }
    }
    .sumbit-buttom {
      border-radius: 8rpx;
      background: $primary-gradient;
      width: 390rpx;
      height: 72rpx;
      margin: 50rpx auto 0;
      @include setlightFont(28rpx, 72rpx, #fff);
      text-align: center;
    }
  }
  .isOutOfRangeClockIn-panel {
    box-sizing: border-box;
    padding: 28rpx 20rpx;
    .title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;
      @include setlightFont(28rpx, 32rpx, $uni-text-color);
      .sure {
        @include setlightFont(28rpx, 32rpx, #276cac);
      }
    }
    .text-panel {
      // border: 2rpx solid $uni-text-color-placeholder;
      border-radius: 16rpx;
      .textarea-panel {
        width: 100%;
        height: 300rpx;
        box-sizing: border-box;
        padding: 16rpx;
      }
    }
  }
}
</style>
