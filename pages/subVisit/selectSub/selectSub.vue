<template>
  <view class="selectClient-panel">
    <view class="page-content">
      <view class="top-panel">
        <view class="search-panel">
          <p-search
            placeholderText="请输入下属员工姓名"
            @searchSumbit="acceptSearch"
          ></p-search>
        </view>
      </view>
      <view class="com" v-if="IsRecommend === '1'">
        <view class="title">
          <image
            class="tuiJ"
            src="/static/image/icon/tuijian.png"
            mode=""
          ></image
          >推荐协访</view
        >
      </view>
      <view v-if="shopList.length === 0" class="task-panel">
        <image class="empty" src="/static/image/bgs/empty.png" mode=""></image>
        <view class="empty-text">暂无数据<br />请联系管理员</view>
      </view>
      <view v-if="shopList.length > 0" class="bottom-panel">
        <scroll-view
          class="button-groups"
          scroll-y="true"
          @scrolltolower="throttledLoadMore"
          lower-threshold="200"
        >
          <!-- 业绩达成排行榜-->
          <view class="button-item" v-for="(e, index) in shopList" :key="e.id">
            <view class="header">
              <view>
                <span class="name">{{ e.personName }}</span>
                <span class="user-tag"> {{ e.postName }}</span>
              </view>
            </view>
            <view class="area">{{ e.departNames }}</view>
            <view class="time" v-if="IsRecommend === '1'">
              <text class="user-tag-success">正在拜访</text>
              {{ e.purMerchantName }}/{{ e.baseHospitalName }}/{{
                e.doctorName
              }}
            </view>
            <view class="start" @click="toDetail(e)">
              <image
                class="start-icon"
                src="/static/image/icon/start.png"
                mode=""
              ></image
              ><text class="start-word">开始协访</text>
            </view>
          </view>
        </scroll-view>
      </view>
      <view v-if="loading" class="uv-picker--loading">
        <uv-loading-icon mode="circle"></uv-loading-icon>
      </view>
      <view class="placeholder-view"></view>
      <p-visit></p-visit>
    </view>
    <p-tabbar tabbarSelect="work"></p-tabbar>
  </view>
</template>

<script setup>
import { onLoad, onShow } from "@dcloudio/uni-app";
import { ref, reactive } from "vue";
import { getCoachedUserWechatVo } from "/common/api/agent/index.js";
import { useVisitStore } from "@/common/store/visit";
import { useUserStore } from "@/common/store/user";
import { signInfo } from "/common/api/sign/index.js";
const shopList = ref([]);
const loading = ref(false);
const pagination = reactive({
  currentPage: 1,
  totalItems: 0,
  pageSize: 15,
});

const visitStore = useVisitStore();
const userStore = useUserStore();
const keyword = ref("");
const IsRecommend = ref(null);
const throttledLoadMore = () => {
  if (loading.value) return;
  pagination.currentPage++;
  getList();
};

const acceptSearch = (inptKeyword) => {
  console.log("搜索条件：keyword", inptKeyword);
  keyword.value = inptKeyword;
  pagination.currentPage = 1;
  shopList.value = [];
  getList(inptKeyword);
};
const getList = async (name) => {
  const obj = uni.getStorageSync("USER__INFO__");
  if (loading.value) {
    return;
  }
  loading.value = true;
  try {
    let parmas = {
      departmentId: userStore.getUserInfo.departmentId,
      postId: userStore.getUserInfo.posts[0].id,
      userName: name || "",
      limit: pagination.currentPage,
      size: pagination.pageSize,
    };
    console.log(userStore.getUserInfo);
    let { code, data } = await getCoachedUserWechatVo(parmas);
    loading.value = false;
    if (code == 0) {
      IsRecommend.value = data?.IsRecommend;
      shopList.value = shopList.value.concat(data?.pageList?.list || []);
    }
  } catch (error) {
    console.error("Error:", error);
  }
};
const toDetail = (item) => {
  if (IsRecommend.value === "1") {
    signInfo({ id: item.id }).then((res) => {
      const { code, data } = res;
      if (code == 0) {
        // visitStore.setVisitType(2);
        // visitStore.setSiginType(2);
        visitStore.setNeedRefresh(true);
        visitStore.setIsIndex(false);
        visitStore.setClientShop({
          id: data.purMerchantId,
          name: data.purMerchantName,
          address: data.destination,
          latitude: data.purMerchantLatitude,
          longitude: data.purMerchantLongitude,
          userId: data.coachedPersonId,
          userName: data.coachedPersonName,
          agentId: data.agentEmployeeId,
          agentName: data.agentEmployeeName,
          createDate: data.createDate,
          locationList: null,
        });
        visitStore.setClientDept({
          departmentId: data.baseHospitalId,
          departmentName: data.baseHospitalName,
        });
        visitStore.setClientDoctor({
          ...visitStore.$state.clientDoctor,
          name: data.doctorName,
          id: data.doctorId,
          init: true,
        });
        visitStore.setAgent({
          agentEmployeeId: data.agentEmployeeId,
          agentEmployeeName: data.agentEmployeeName,
          agentId: data.agentId,
          agentName: data.agentName,
          isAgent: data.agentIsPresent,
        });
        visitStore.setAgentEmployee({
          personName: item.personName,
          departNames: item.departNames,
          isScene: data.crmIsPresent,
          personId: item.personId,
          id: data.id,
          personCode: data.personCode,
        });
        uni.reLaunch({
          url: `/pages/subVisit/createVisit/createVisit`,
        });
      }
    });
  } else {
    visitStore.setAgentEmployee(item);
    uni.navigateTo({
      url: `/pages/subVisit/selectClientShop/selectClientShop?terminalUserId=${item.personId}&personName=${item.personName}`,
    });
  }
};
onLoad(() => {
  getList();
});
onShow(() => {
  visitStore.setActiveTab(0);
});
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";

.com {
  .title {
    margin-top: 20rpx;
    display: flex;
    align-items: center;
    margin-left: 20rpx;
    color: var(---white, #fff);

    /* 点文本-加粗/14pt bold */
    font-family: "PingFang SC";
    font-size: 28rpx;
    font-style: normal;
    font-weight: 600;
    .tuiJ {
      width: 48rpx;
      height: 48rpx;
      margin-right: 10rpx;
    }
  }
}
.start {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-bottom: 20rpx;
  margin-right: 20rpx;
  &-icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 10rpx;
  }
  &-word {
    color: var(----, #0d6ce4);

    /* 点文本-常规/14pt regular */
    font-family: "PingFang SC";
    font-size: 28rpx;
    font-style: normal;
    font-weight: 400;
    text-decoration-line: underline;
    text-decoration-style: solid;
    text-decoration-skip-ink: none;
    text-decoration-thickness: auto;
    text-underline-offset: auto;
    text-underline-position: from-font;
  }
}
.placeholder-view {
  width: 100%;
  height: 90rpx;
}
.button-groups {
  overflow: scroll;
  height: 100%;
  box-sizing: border-box;
  padding: 0 20rpx;

  .button-item {
    @include cardBgCommonStyle();
    margin: 20rpx 0;

    .setting-text {
      @include setBoldFont(26rpx, 36rpx, #2f3133);
    }

    .header {
      display: flex;
      justify-content: space-between;
      padding: 20rpx 20rpx 0 20rpx;

      .name {
        @include setBoldFont(28rpx, 36rpx, #1d1d1d);
      }

      .header-time {
        color: var(---Gray6, #8d9094);
        font-size: 24rpx;
      }

      .user-tag {
        @include setBoldFont(20rpx, 38rpx, #4068f5);
        border-radius: 8rpx;
        height: 38rpx;
        min-width: 50rpx;
        margin-left: 20rpx;
        text-align: center;
        padding: 3rpx 8rpx;
      }
    }
    .user-tag-success {
      display: inline-flex;
      width: 108rpx;
      text-align: center;
      line-height: 36rpx;
      justify-content: center;
      align-items: center;
      gap: 14px;
      color: var(---white, #fff);
      border-radius: 20px;
      background: var(----, #19b21e);
      margin-right: 10rpx;
    }
    .area {
      padding: 10rpx 20rpx 14rpx 20rpx;
      border-bottom: 1px solid #ced4db;
      color: var(---Gray6, #8d9094);
      font-size: 20rpx;
    }

    .time {
      padding: 12rpx 20rpx 16rpx 20rpx;
      display: flex;
      align-items: center;
      color: var(---Gray7, #2f3133);
      box-sizing: border-box;
      font-size: 20rpx;
    }
  }
}
.selectClient-panel {
  .page-content {
    @include globalPageStyle();
    display: flex;
    flex-direction: column;

    .top-panel {
      z-index: 1;
      padding: 26rpx 24rpx 18rpx;
      box-sizing: border-box;
      background: rgba(255, 255, 255, 0.9);
      /* 下层投影 */
      box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(0, 0, 0, 0.12);
      display: flex;
      flex-direction: column;

      .search-panel {
        ::v-deep .search-panel {
          // display: none;
          background: transparent;
          padding: 0;
          box-shadow: none;
        }
      }
    }

    .bottom-panel {
      flex: 1;
      overflow-y: hidden;

      .list-panel {
        box-sizing: border-box;
        padding: 28rpx 20rpx;
        padding-right: 0;
        height: 100%;

        .content-box {
          box-sizing: border-box;
          padding-right: 20rpx;
          height: 100%;
        }
      }
    }
  }
  .empty {
    width: 230rpx;
    height: 200rpx;
    margin: 26vh auto 20rpx;
    display: flex;
  }
  .empty-text {
    color: var(---white, #fff);
    text-align: center;

    /* 点文本-加粗/12pt bold */
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 166.667% */
  }
}
</style>
