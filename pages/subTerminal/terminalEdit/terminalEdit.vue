<template>
	<view class="terminalEdit-panel">
		<view class="page-content">
			<view class="meeting-info">
				<van-cell v-for="(item, key) in terminalInfos" :key="key" :is-link="item.isLink && !isInputField(key)" @tap="!isInputField(key) && triggerCell(key, item)">
					<view slot="title">
						<view class="van-cell-text">
							<text class="stress-text">*</text>
							{{ item.title }}
						</view>
					</view>
          <!-- 如果是输入框项，显示输入框 -->
          <input v-if="isInputField(key)" v-model="item.value" :placeholder="item.isLink ? '请选择' : '请输入'" class="meeting-input" maxlength="30" />
          <!-- 否则显示文本 -->
          <text v-else>{{ item.label }}</text>
				</van-cell>
			</view>
			<view class="submit-button" @tap="submitTerminalEdit">提交</view>
		</view>
		<p-tabbar tabbarSelect="work"></p-tabbar>
    <uv-action-sheet ref="actionSheet" :actions="terminalOptions.type.map(o => ({ ...o, name:o.label }))" @select="handleTypeSelect"/>
	</view>
</template>

<script setup>
import { ref } from 'vue';
import {
  onLoad
} from '@dcloudio/uni-app';
import {terminalInfo, updateCrmTerminal} from "@/common/api/terminal";

const isInputField = (key) => ['name'].includes(key);

const terminalInfos = ref({
	name: { title: '终端名称', value: '', label: '', isLink: false },
  address: { title: '详细地址', value: '', label: '', isLink: true },
  // property: { title: '终端性质', value: '', label: '', isLink: true },
  level: { title: '终端级别', value: '', label: '', isLink: true },
  grade: { title: '终端等别', value: '', label: '', isLink: true },
  type: { title: '客户类型', value: '', label: '', isLink: true },
});

const id = ref(null)

const terminalOptions = {
  property: [
    { label: '等级医院', value: '1' },
    { label: '基层医疗', value: '2' },
    { label: '民营医疗', value: '3' },
  ],
  level: [
    { label: '三级', value: '100000001' },
    { label: '二级', value: '100000002' },
    { label: '一级', value: '100000003' },
    { label: '未定级', value: '100000009' }
  ],
  grade: [
    { label: '特等', value: '100000000' },
    { label: '甲等', value: '100000001' },
    { label: '乙等', value: '100000002' },
    { label: '丙等', value: '100000003' },
    { label: '无等别', value: '100000009' }
  ],
  type: [
    { label: '医药商业', value: '1' },
    { label: '医院', value: '2' },
    { label: '药店', value: '3' },
    { label: '基层医疗卫生机构', value: '4' },
    { label: '个体', value: '5' },
    { label: '物流公司', value: '11' },
    { label: '快递公司', value: '12' },
    { label: '方舱医院', value: '13' },
    { label: '其他', value: '99' },
  ]
};

const actionSheet = ref()

/**
 * @description 触发 `van-cell` 的点击事件
 * @param {string} key - 触发的键名
 * @param {object} item - 触发的项
 */
const triggerCell = (key, item) => {
	console.log(`点击了 ${item.title}，key：${key}，值：${item.value}`);
	switch (key) {
    case 'type':
      actionSheet.value.open()
      break
		case 'name':
			break;
    case 'address':
      uni.chooseLocation({success: (location,a,b,c) => {
        const { address, name } = location
          terminalInfos.value.address = {
            ...terminalInfos.value.address,
            ...location,
            label: `${address}${name}`,
            value:`${address}${name}`
          }
        }});
      break;
		default:
      uni.showActionSheet({
        itemList: terminalOptions[key].map((o) => o.label),
        success: (res) => {
          const selectedType = terminalOptions[key][res.tapIndex];
          terminalInfos.value[key] = { ...terminalInfos.value[key],...selectedType };
        }
      });
	}
};

const handleTypeSelect = (selectedType) => {
  terminalInfos.value.type = { ...terminalInfos.value.type,...selectedType };
}

const getTerminalDetailInfo = () => {
  terminalInfo({
    id: id.value
  }).then(res => {
    const address = res.data.crmTerminalLocationList && res.data.crmTerminalLocationList.length ? res.data.crmTerminalLocationList[0] : {}

    terminalInfos.value.name.value = res.data.name
    terminalInfos.value.name.label = res.data.name
    terminalInfos.value.address.value = address.address
    terminalInfos.value.address.label = address.address
    terminalInfos.value.address.latitude = address.latitude
    terminalInfos.value.address.longitude = address.longitude
    // terminalInfos.value.property.value = res.data.property
    // terminalInfos.value.property.label = res.data.propertyName
    terminalInfos.value.level.value = res.data.level
    terminalInfos.value.level.label = res.data.levelName
    terminalInfos.value.grade.value = res.data.grade
    terminalInfos.value.grade.label = res.data.gradeName
    terminalInfos.value.type.value = res.data.type
    terminalInfos.value.type.label = res.data.typeName
  })
}

const submitTerminalEdit = () => {

  Object.keys(terminalInfos.value).forEach(childKey => {
    const childItem = terminalInfos.value[childKey];
    if (!childItem.value) {
      uni.showToast({ title: `${childItem.title}不能为空`, icon: 'none' });
      throw new Error(`${childItem.title}不能为空`)
    }
  })

  updateCrmTerminal(
      {
        id: id.value,
        name: terminalInfos.value.name.value,
        address: terminalInfos.value.address.value,
        // property: terminalInfos.value.property.value,
        // propertyName: terminalInfos.value.property.label,
        level: terminalInfos.value.level.value,
        levelName: terminalInfos.value.level.label,
        grade: terminalInfos.value.grade.value,
        gradeName: terminalInfos.value.grade.label,
        type: terminalInfos.value.type.value,
        typeName: terminalInfos.value.type.label,
        crmTerminalLocationList: [
          {
            address: terminalInfos.value.address.value,
            latitude: terminalInfos.value.address.latitude,
            longitude: terminalInfos.value.address.longitude,
          }
        ]
      }
  ).then(res => {
    if(res.code == 0) {
      uni.showToast({
        title: '修改成功',
        icon: 'none',
        duration: 2000
      })
      setTimeout(() => {
        const pages = getCurrentPages();
        const prePage = pages[pages.length - 2];
        prePage.$vm.refresh()
        setTimeout(() => {
          uni.navigateBack();
        }, 2000)
      },2000)
    } else {
      uni.showToast({
        title: '修改失败',
        icon: 'none'
      })
    }
  })
};

onLoad((option) => {
  id.value = option.id
  getTerminalDetailInfo()
})
</script>

<style scoped lang="scss">
@import '@/static/scss/global.scss';
.terminalEdit-panel {
	.page-content {
		@include globalPageStyle();
		padding: 28rpx 20rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		.meeting-info {
			@include cardBgCommonStyle();
			box-sizing: border-box;
			padding: 0 8rpx;
			margin-bottom: 24rpx;

			:deep(.van-cell) {
				--cell-vertical-padding: 20rpx;
				--cell-horizontal-padding: 28rpx;
				--cell-background-color: transparent;
				border-bottom: 2rpx solid #ced4db;

				.stress-text {
					@include setBoldFont(28rpx, 44rpx, #f00);
					position: absolute;
					left: -14rpx;
				}
				.van-cell-text {
					position: relative;
					@include setBoldFont(28rpx, 44rpx, #2f3133);
				}

				// .van-cell__title {
				// 	@include setBoldFont(28rpx, 44rpx, #2f3133);
				// 	&::before {
				// 		content: '*';
				// 		@include setBoldFont(28rpx, 44rpx, #f00);
				// 	}
				// }
				.van-cell__value {
					@include setlightFont(28rpx, 44rpx, #8d9094);
				}
				.meeting-theme-input {
					@include setlightFont(28rpx, 44rpx, #8d9094);
				}
			}
			/* 让最后一个 van-cell 去掉 border */
			:deep(.van-cell:last-child) {
				border-bottom: none;
			}
		}
		.submit-button {
			@include commonSquareButtonStyle();
		}
	}
}
</style>
