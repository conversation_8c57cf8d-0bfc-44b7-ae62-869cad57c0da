import {
	http
} from '@/common/request/index.js'; // 局部引入


const api = {
	taskmanagementpersionPage: '/wechat/taskmanagementpersion/page',
	getBdmSalesmanTaskPersonVoByTime: '/wechat/taskmanagementpersion/getBdmSalesmanTaskPersonVoByTime',
	taskmanagementpersionInfo: '/wechat/taskmanagementpersion/info',
	taskmanagementpersionAdd: '/wechat/taskmanagementpersion/add',
	taskmanagementpersionreportPage: '/wechat/taskmanagementpersionreport/page',
	taskmanagementpersionreportAddInfo: '/wechat/taskmanagementpersionreport/getAddWechatInfo',
	taskmanagementpersionreportAdd: '/wechat/taskmanagementpersionreport/add',
	taskmanagementpersionreportInfo: '/wechat/taskmanagementpersionreport/getWechatInfo',
	taskmanagementpersionDelete: '/wechat/taskmanagementpersion',
}

export const taskmanagementpersionPage = (data) => {
	return http.post(api.taskmanagementpersionPage, data);
}
export const getBdmSalesmanTaskPersonVoByTime = (data) => {
	return http.post(api.getBdmSalesmanTaskPersonVoByTime, data);
}
export const taskmanagementpersionInfo = (params) => {
	return http.get(api.taskmanagementpersionInfo, {params});
}
export const taskmanagementpersionAdd = (data) => {
	return http.post(api.taskmanagementpersionAdd, data);
}

export const taskmanagementpersionreportPage = (data) => {
	return http.post(api.taskmanagementpersionreportPage, data);
}
export const taskmanagementpersionreportAddInfo = (data) => {
	return http.post(api.taskmanagementpersionreportAddInfo, data);
}

export const taskmanagementpersionreportAdd = (data) => {
	return http.post(api.taskmanagementpersionreportAdd, data);
}

export const taskmanagementpersionreportInfo = (params) => {
	return http.get(api.taskmanagementpersionreportInfo, {params});
}
export const taskmanagementpersionDelete = (params) => {
	return http.get(api.taskmanagementpersionDelete, { params });
}


