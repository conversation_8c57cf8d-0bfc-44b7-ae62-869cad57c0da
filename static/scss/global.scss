$primary-color-1: #52acff;
$primary-color-2: #263af0;
$primary-gradient: linear-gradient(180deg, #52acff -12%, #263af0 117%);

// 文本 n 行溢出隐藏
@mixin ellipsisBasic($clamp: 2) {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: $clamp;
}

// 全局页面样式
@mixin globalPageStyle() {
	// 126rpx tabbar高度
	height: calc(100vh - env(safe-area-inset-bottom) - 126rpx);
	width: 100%;
	box-sizing: border-box;
}

// 绝对定位水平居中
@mixin absoluteHorizontalCenter() {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

// 绝对定位铺满
@mixin absoluteFull() {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
}

// 绝对定位垂直居中
@mixin absoluteVerticalCenter() {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
}

// 绝对定位水平垂直居中
@mixin absoluteCenter() {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

// 通用提交那妞 圆的
@mixin commonButtonStyle() {
	width: 100%;
	height: 72rpx;
	text-align: center;
	color: $uni-text-color-inverse;
	font-feature-settings: 'liga' off, 'clig' off;
	font-family: 'PingFangSC-Semibold';
	font-size: 28rpx;
	font-style: normal;
	font-weight: 400;
	line-height: 72rpx;
	border-radius: 44rpx;
	background: $primary-gradient;
	box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(0, 0, 0, 0.12);
}
// 通用提交那妞 方的
@mixin commonSquareButtonStyle() {
	border-radius: 8rpx;
	background: $primary-gradient;
	height: 72rpx;
	line-height: 72rpx;
	color: #fff;
	font-feature-settings: 'liga' off, 'clig' off;
	font-family: 'PingFangSC-Semibold';
	font-size: 28rpx;
	font-style: normal;
	font-weight: 400;
	box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(0, 0, 0, 0.12);
	text-align: center;
}

// 小按钮
@mixin commonMiniButtonStyle() {
	text-align: center;
	border-radius: 4rpx;
	background: #c8c8c8;
	width: 92rpx;
	height: 36rpx;
	font-weight: 500;
	font-family: 'PingFangSC-Semibold', 'Source Han Sans Blod';
	color: $uni-text-color-inverse;
	font-size: 24rpx;
	line-height: 36rpx;
	&.finish {
		background: linear-gradient(180deg, #ffd952 -12%, #f05626 117%);
	}
}

@mixin setBackgroundImage($bg) {
	background-image: url(#{$bg});
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 100% 100%;
}

@mixin cardBgCommonStyle($bgColor: rgba(255, 255, 255, 0.9)) {
	border-radius: 16rpx;
	background: $bgColor;
	box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(0, 0, 0, 0.12);
	box-sizing: border-box;
}

//加粗字体
@mixin setBoldFont($fontSize: 28rpx, $lineHeight: 36rpx, $color: $uni-text-color) {
	font-family: 'PingFangSC-Semibold', 'Source Han Sans Blod';
	color: $color;
	font-size: $fontSize;
	line-height: $lineHeight;
}
// 常规字体
@mixin setlightFont($fontSize: 28rpx, $lineHeight: 36rpx, $color: $uni-text-color) {
	font-family: 'PingFangSC-Regular';
	color: $color;
	font-size: $fontSize;
	line-height: $lineHeight;
	font-weight: 400;
}

:root {

	--primary-color: #007aff;

	--sidebar-selected-border-color: var(--primary-color);
	--tree-select-item-active-color: var(--primary-color);
	--button-primary-background-color: var(--primary-color);
	--button-primary-border-color: var(--primary-color);
}
