<template>
  <view class="teamMeetingDetails-panel">
    <view class="page-content">
      <view class="top-panel">
        <view class="meeting-detail-panel">
          <view
            class="meeting-info"
            v-for="(group, groupKey) in allMeetingData"
            :key="groupKey"
          >
            <van-cell
              v-for="(item, key) in group"
              :key="key"
              :is-link="item.isLink && !isInputField(key)"
              :title="item.title"
              title-width="120rpx"
            >
              <!-- 如果是输入框项（如 会议主题、预算费用、参会人数），显示输入框 -->
              <input
                v-if="isInputField(key) && !item.readonly"
                v-model="item.value"
                placeholder="请输入"
                class="meeting-input"
                maxlength="30"
              />
              <!-- 否则显示文本 -->
              <text v-else>{{ item.label }}</text>
            </van-cell>
          </view>
          <!-- 上传图片？是否要保留 -->
          <!--					<view class="add-panel">-->
          <!--						<view class="img-item">-->
          <!--							<view class="left-content">-->
          <!--								<image class="thumbnail" src="/pages/subVisit/static/iocn_assistanceReview.png" mode=""></image>-->
          <!--								<text class="text">会议中.jpg</text>-->
          <!--							</view>-->
          <!--							<image class="detele" src="../static/icon_close.png" mode=""></image>-->
          <!--						</view>-->
          <!--&lt;!&ndash;						<view class="add-btn">&ndash;&gt;-->
          <!--&lt;!&ndash;							<view class="icon-panel">&ndash;&gt;-->
          <!--&lt;!&ndash;								<image class="icon" src="../static/icon_add.png" mode=""></image>&ndash;&gt;-->
          <!--&lt;!&ndash;							</view>&ndash;&gt;-->
          <!--&lt;!&ndash;							<text>添加会议附件</text>&ndash;&gt;-->
          <!--&lt;!&ndash;						</view>&ndash;&gt;-->
          <!--					</view>-->
          <!--					<view class="remark-panel">-->
          <!--						<uv-textarea v-model="meetingSummarize" placeholder="请输入会议总结" count maxlength="500" adjustPosition border="none"></uv-textarea>-->
          <!--					</view>-->
        </view>

        <view class="steps-panel" v-if="approvalList.length">
          <view class="title">审批流程</view>
          <view class="content">
            <view
              class="step-item"
              v-for="(item, index) in approvalList"
              :key="index"
            >
              <view class="index-num">{{ index + 1 }}</view>
              <view class="item-detail">
                <view class="name">
                  <text class="stress-text">{{ item.activityName }}</text>
                  <text>{{ item.nodes[0].approvaler[0].name }}</text>
                </view>
                <view class="status">
                  <!--									<text>审批意见</text>-->
                  <text class="stress-text">{{
                    APPROVAL_STATUS[item.nodes[0].displayStatus]
                  }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view>
        <view
          class="cancel-btn"
          v-if="MeetingStatus == 3"
          style="background-color: #fff; color: #000"
          @click="meetingCancelClick"
        >
          <text>取消会议</text>
        </view>
        <view
          class="bottom-panel"
          v-if="shouldShowBottomPanel"
          @click="meetingButtonClick"
        >
          <text>{{ MeetingButtonText }}</text>
        </view>
        <view
          class="bottom-panel"
          v-if="meetingInfo.status == 4"
          @click="finish"
        >
          <text>结束会议</text>
        </view>
      </view>
    </view>
    <p-tabbar tabbarSelect="work"></p-tabbar>
  </view>
</template>

<script setup>
// 初始化获取当前这个会议的状态
// 然后将对应的值注入到meetingAttend和meetingBudget，
// 进行中和已召开通过readonly区分，如果是进行中readonly赋值false 、 已召开赋值true
// 审批驳回的会议，点击修改，跳转到会议详情页，可以服用
import { computed, ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import {
  academicconferencesinfo,
  bdmteamconferencesinfoInfo,
  getApproveUrl,
  teamUpdateStatus,
  updateStatus,
} from "@/common/api/meeting";

const MEETING_STATUS = {
  0: "草稿",
  1: "审批中",
  2: "审批驳回",
  3: "待执行",
  4: "进行中",
  5: "已召开",
  6: "已取消",
};
const MeetingStatus = ref("3");
const meetingSummarize = ref("");
const isVisibleMoreButton = ref(true);
const id = ref(null);
const meetingType = ref(null);
const approvalList = ref([]);
// 审批流程步骤
const steps = [
  { text: "地区主管", desc: "赵子龙" },
  { text: "省区主管", desc: "艾德" },
];
const meetingAttend = ref({
  teamMeetingName: {
    title: "会议类型",
    value: "",
    label: "",
    isLink: false,
    readonly: true,
  },
  meetName: {
    title: "会议名称",
    value: "",
    label: "",
    isLink: false,
    readonly: true,
  },
  teamMeetingTime: {
    title: "会议时间",
    value: "",
    label: "",
    isLink: false,
    readonly: true,
  },
  personChargeName: {
    title: "负责人",
    value: "",
    label: "",
    isLink: false,
    readonly: true,
  },
  userNames: {
    title: "参会人",
    value: "",
    label: "",
    isLink: false,
    readonly: true,
  },
  meetingModeName: {
    title: "会议模式",
    value: "",
    label: "",
    isLink: false,
    readonly: true,
  },
});

const meetingBudget = ref({
  teamAccountname: {
    title: "预算科目",
    value: "",
    label: "",
    isLink: false,
    readonly: true,
  },
  estimatedCost: {
    title: "预算费用",
    value: "",
    label: "",
    isLink: false,
    readonly: true,
  },
  fileId: {
    title: "预算明细",
    value: "",
    label: "",
    isLink: false,
    readonly: true,
  },
});

const meetingInfo = ref({});

// 计算所有会议相关数据（方便 `v-for` 渲染）
const allMeetingData = computed(() => ({
  meetingAttend: meetingAttend.value,
  meetingBudget: meetingBudget.value,
}));

/**
 * @description 计算底部按钮文本
 */
const MeetingButtonText = computed(() => {
  switch (MeetingStatus.value) {
    case "0":
    case "2":
      return "去修改";
    case "3":
      return "去执行";
    default:
      return "";
  }
});

/**
 * @description 判断是否显示底部按钮
 */
const shouldShowBottomPanel = computed(() =>
  ["0", "2", "3"].includes(MeetingStatus.value)
);

/**
 * @description 判断是否是输入框字段（避免误触弹出 `showActionSheet`）
 * @param {string} key - 字段名
 * @returns {boolean}
 */
const isInputField = (key) => ["meetingPeople", "meetingCost"].includes(key);

const updateMeetingStatus = (status) => {
  teamUpdateStatus({
    id: id.value,
    status,
  }).then((res) => {
    if (res.code === 0) {
      const pages = getCurrentPages();
      const prePage = pages[pages.length - 2];
      prePage.$vm.refresh();
      uni.navigateBack();
    } else {
      uni.showToast({
        title: "失败",
        icon: "none",
      });
    }
  });
};

const meetingCancelClick = () => {
  uni.showModal({
    title: "提示",
    content: "确定取消会议吗？",
    success: (res) => {
      if (res.confirm) {
        updateMeetingStatus(6);
      } else if (res.cancel) {
        console.log("用户点击取消");
      }
    },
  });
};

const finish = () => {
  uni.showModal({
    title: "提示",
    content: "确定结束会议吗？",
    success: (res) => {
      if (res.confirm) {
        updateMeetingStatus(5);
      } else if (res.cancel) {
        console.log("用户点击取消");
      }
    },
  });
};

const meetingButtonClick = () => {
  switch (MeetingStatus.value) {
    case "0":
    case "2":
      uni.redirectTo({
        url: `/pages/subMeeting/meetingBooking/meetingBooking?meetingType=team&id=${id.value}`,
      });
      break;
    case "3":
      updateMeetingStatus(4);
      break;
  }
};

const selectTerminalClick = () => {
  uni.navigateTo({
    url: `/pages/subMeeting/selectTerminal/selectTerminal`,
  });
};

const selectTerminal = (selection) => {
  if (!selection || selection.length === 0) {
    return;
  }
  meetingAttendees.value.meetingTerminal = {
    ...meetingAttendees.value.meetingTerminal,
    label: selection.map((item) => item.name).join("\n"),
    value: selection.map((item) => item.id).join(","),
  };
};

defineExpose({
  selectTerminal,
});

onLoad((option) => {
  id.value = option.id;
  meetingType.value = option.meetingType;
  bdmteamconferencesinfoInfo({ id: option.id }).then((res) => {
    if (res.data.meetingModeType == "1") {
      meetingAttend.value.meetingConnect = {
        title: "会议链接",
        value: "",
        label: "",
        isLink: false,
        readonly: true,
      };
    } else {
      meetingAttend.value.meetingAddress = {
        title: "会议地址",
        value: "",
        label: "",
        isLink: false,
        readonly: true,
      };
    }
    MeetingStatus.value = res.data.status;
    Object.keys(res.data).forEach((key) => {
      Object.keys(meetingAttend.value).forEach((mKey) => {
        if (key === mKey) {
          meetingAttend.value[mKey].label = res.data[key];
        }
      });
      Object.keys(meetingBudget.value).forEach((mKey) => {
        if (key === mKey) {
          meetingBudget.value[mKey].label = res.data[key];
        }
      });
    });
    meetingAttend.value.meetName.label = res.data.meetName;
    meetingBudget.value.fileId.label = res.data.fileName;
    meetingBudget.value.teamAccountname.label = res.data.teambudgetAccountName;
    if (res.data.traceId) {
      getApproveUrl({ workflowInstanceId: res.data.traceId }).then((res) => {
        approvalList.value = res.data.data;
      });
    }
    meetingInfo.value.status = res.data.status;
  });
});
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";
.teamMeetingDetails-panel {
  .page-content {
    @include globalPageStyle();
    box-sizing: border-box;
    padding: 28rpx 20rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow-y: auto;
    .meeting-detail {
      @include cardBgCommonStyle();
      padding: 20rpx 28rpx;
      @include cardBgCommonStyle();
      margin-bottom: 24rpx;
      position: relative;
      .meet-status {
        position: absolute;
        right: 20rpx;
        top: 20rpx;
        @include setBoldFont(28rpx, 40rpx, #fff);

        display: flex;
        justify-content: flex-start;
        align-items: center;
        .text-info {
          height: 40rpx;
          border-radius: 54rpx;
          padding: 0rpx 12rpx;
          &.PENDING_APPROVAL {
            background: linear-gradient(99deg, #9cb8ff 11.03%, #2d4be9 88.97%);
          }
          &.REJECTED {
            background: linear-gradient(99deg, #ff9c9c 11.03%, #e92d2d 88.97%);
          }
          &.UPCOMING {
            background: linear-gradient(99deg, #ffdd9c 11.03%, #e9bf2d 88.97%);
          }
          &.IN_PROGRESS {
            background: linear-gradient(99deg, #9eff9c 11.03%, #2de94b 88.97%);
          }
          &.COMPLETED,
          &.CANCEL {
            background: #b4b9bf;
          }
        }

        .more-btn {
          margin-left: 12rpx;
          position: relative;
          .icon {
            width: 48rpx;
            height: 48rpx;
          }
          .more-button {
            position: absolute;
            right: 0;
            top: 16px;
            .cancel-meeting {
              width: 120rpx;
              padding: 8rpx;
              border-radius: 8rpx;
              border: 2rpx solid #fc474c;
              padding: 8rpx;
              @include setBoldFont(24rpx, 40rpx, #fc474c);
              text-align: center;
            }
          }
        }
      }
      .meet-lable {
        @include setBoldFont(24rpx, 40rpx, #4068f5);
      }
      .meet-name {
        margin-bottom: 8rpx;
        @include setBoldFont(28rpx, 44rpx, #2f3133);
      }
      .meet-info,
      .meet-time {
        margin-top: 8rpx;
        @include setlightFont(24rpx, 40rpx, #8d9094);
        .stress-text {
          @include setBoldFont(24rpx, 40rpx, #2f3133);
        }
      }
      .meet-info:first-child {
        margin-right: 50rpx;
      }
      .meeting-des {
        @include setlightFont(24rpx, 40rpx, #4068f5);
        text-decoration: underline;
        text-align: center;
        margin-top: 8rpx;
      }
    }
    .meeting-detail-panel {
      .meeting-info {
        @include cardBgCommonStyle();
        box-sizing: border-box;
        padding: 0 8rpx;
        margin-bottom: 24rpx;

        :deep(.van-cell) {
          --cell-vertical-padding: 20rpx;
          --cell-horizontal-padding: 28rpx;
          --cell-background-color: transparent;
          border-bottom: 2rpx solid #ced4db;

          .van-cell__title {
            @include setBoldFont(28rpx, 44rpx, #2f3133);
          }
          .van-cell__value {
            @include setlightFont(28rpx, 44rpx, #8d9094);
          }
          .meeting-theme-input {
            @include setlightFont(28rpx, 44rpx, #8d9094);
          }
        }
        /* 让最后一个 van-cell 去掉 border */
        :deep(.van-cell:last-child) {
          border-bottom: none;
        }
      }
      .add-panel {
        @include cardBgCommonStyle();
        box-sizing: border-box;
        padding: 0 8rpx;
        .add-btn,
        .img-item {
          padding: 20rpx 24rpx;
          @include setBoldFont(28rpx, 44rpx, #2f3133);
          display: flex;
          justify-content: flex-start;
          align-items: center;
        }
        .img-item {
          border-bottom: 2rpx solid #ced4db;
          justify-content: space-between;
          .left-content {
            display: flex;
            justify-content: flex-start;
            align-items: center;
          }
          .thumbnail {
            width: 48rpx;
            height: 48rpx;
            margin-right: 12rpx;
          }
          .text {
            @include setlightFont(28rpx, 40rpx, #2f3133);
          }
          .detele {
            width: 32rpx;
            height: 32rpx;
          }
        }

        .add-btn {
          .icon-panel {
            width: 48rpx;
            height: 48rpx;
            margin-right: 12rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            .icon {
              width: 24rpx;
              height: 24rpx;
            }
          }
        }
      }
      .remark-panel {
        margin-top: 24rpx;
        @include cardBgCommonStyle();
        height: 254rpx;
        :deep(uv-textarea) {
          background-color: transparent !important;
          height: 100%;
          width: 100%;
          box-sizing: border-box;
          padding: 24rpx 28rpx;
          .uv-textarea__field {
            height: 100% !important;
          }
          .textarea-placeholder {
            @include setlightFont(28rpx, 32rpx, rgba(29, 29, 29, 0.6));
          }
          .uv-textarea__count {
            background-color: transparent !important;
          }
        }
      }
    }
    .steps-panel {
      @include cardBgCommonStyle();
      padding: 20rpx 28rpx;
      .title {
        @include setBoldFont(28rpx, 44rpx, #2f3133);
        margin-bottom: 22rpx;
      }
      .content {
        .step-item {
          display: flex;
          height: 86rpx;
          position: relative;
          margin-bottom: 70rpx;
          // 只有从第二个元素开始才有 `::after`
          &:not(:first-child)::before {
            content: "";
            width: 4rpx;
            height: 138rpx;
            top: -130rpx;
            //background: #ced4db;
            background-color: #007aff;
            /* 下层投影 */
            box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(0, 0, 0, 0.12);
            position: absolute;
            left: 22rpx;
          }
          &:last-child {
            margin-bottom: 0rpx;
          }

          .index-num {
            z-index: 1;
            text-align: center;
            width: 40rpx;
            height: 40rpx;
            border: 2rpx solid #fff;
            border-radius: 50%;
            background: #d9d9d9;
            @include setlightFont(21rpx, 40rpx, #fff);
            margin-right: 22rpx;
            &.active {
              background: #38b865;
            }
          }
          .item-detail {
            display: flex;
            flex-direction: column;
            .name,
            .status {
              @include setlightFont(24rpx, 40rpx, #8d9094);
              .stress-text {
                @include setBoldFont(24rpx, 40rpx, #2f3133);
                &.REJECTED {
                  color: #fc474c;
                }
              }
              :first-child {
                margin-right: 18rpx;
              }
            }
            .status {
              margin-top: 6rpx;
            }
          }
        }
      }
    }
    .cancel-btn {
      border-radius: 4px;
      background-color: #ffffff;
      line-height: 37px;
      color: #000000;
      font-size: 14px;
      text-align: center;
      font-style: normal;
      font-weight: 400;
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
      margin-bottom: 24rpx;
    }
    .bottom-panel {
      @include commonSquareButtonStyle();
    }
  }
}
</style>
