<template>
  <view class="workbenches-page">
    <view class="page-content">
      <view
        :style="{ height: `${safeAreaInsetsTop ? safeAreaInsetsTop : 0}px` }"
      ></view>
      <view class="work-panel">
        <view class="dailyTask">
          <text class="title">日常应用</text>
          <view class="content">
            <view
              class="item"
              v-for="(item, index) in dailyTaskList"
              :key="'dailyTask' + index"
              :style="{ '--text-color': item.textColor }"
              @tap="triggerDailyTask(item.type)"
            >
              <!-- <text>{{ item.title }}</text> -->
              <img :src="item.img" alt="" srcset="" />
            </view>
          </view>
        </view>

        <view class="teamTask">
          <text class="title">团队管理</text>
          <view class="content">
            <view
              class="item"
              v-for="(item, index) in teamTaskList"
              :key="'dailyTask' + index"
              :style="{ '--text-color': item.textColor }"
              @tap="triggerTeamTask(item.type)"
            >
              <!-- <text>{{ item.title }}</text> -->
              <img :src="item.img" alt="" srcset="" />
            </view>
          </view>
        </view>
      </view>
    </view>
    <p-tabbar tabbarSelect="work"></p-tabbar>
  </view>
</template>

<script setup>
import { onLoad, onShow } from "@dcloudio/uni-app";
import { getSafeAreaInsetsTop } from "@/utils/utils.js";
import { ref, reactive } from "vue";
import { useVisitStore } from "@/common/store/visit";
import { getByCurrentTime } from "/common/api/sign/index.js";
import { USER_INFO_KEY } from "/common/const/cache.js";
import { formatTimestamp } from "/utils/utils.js";

const safeAreaInsetsTop = ref(0);
const currentTime = ref("");
const visitStore = useVisitStore();
// visitType 1 终端拜访,2市场协访,3代理商拜访
// 用户信息
const UserInfo = uni.getStorageSync(USER_INFO_KEY); // 从缓存里拿用户信息
const salesmanId = UserInfo && UserInfo.id ? UserInfo.id : null; // 业务员 ID，确保不存在时为 null
const teamTaskList = [
  {
    link: "",
    title: "工作计划",
    textColor: "#23B1FF",
    type: "visit",
    img: "/static/image/workbenches/gongzuojihua.png",
  },
  {
    link: "",
    title: "团队例会",
    textColor: "#2FE191",
    type: "teamMeeting",
    img: "/static/image/workbenches/tuandui.png",
  },
  {
    link: "",
    title: "工作报告",
    textColor: "#B223FF",
    type: "teamReport",
    img: "/static/image/workbenches/gongzuo.png",
  },
];

const dailyTaskList = ref([]);
onShow(() => {
  // 招商代表 10005，省公司经理 10002，推广专员 10006，招商助理 10007，推广副经理 10008，推广经理 10009，事业部总监 10001
  dailyTaskList.value = [
    {
      link: "",
      title: "终端拜访",
      textColor: "#23B1FF",
      type: "terminalVisit",
      img: "/static/image/workbenches/zhongduanbai.png",
    },
    {
      link: "",
      title: "市场协访",
      textColor: "#14EA3F",
      type: "marketVisit",
      img: "/static/image/workbenches/xiefang.png",
    },
    {
      link: "",
      title: "代理商拜访",
      textColor: "#FFB623",
      type: "agentVisit",
      img: "/static/image/workbenches/daili.png",
    },
    {
      link: "",
      title: "终端开发",
      textColor: "#ED5867",
      type: "terminalDev",
      img: "/static/image/workbenches/zhongduankai.png",
    },
    {
      link: "",
      title: "代理商评估",
      textColor: "#ED5867",
      type: "agentPG",
      img: "/static/image/workbenches/agentPG.png",
    },
    {
      link: "",
      title: "学术会议",
      textColor: "#FFB623",
      type: "meeting",
      img: "/static/image/workbenches/xueshu.png",
    },
    {
      link: "",
      title: "代理商档案",
      textColor: "#ED5867",
      type: "agentDA",
      img: "/static/image/workbenches/agentDA.png",
    },
  ];
  const postCode = UserInfo.posts[0].code; // 获取岗位编码
  if (["10006", "10007", "10008", "10009"].includes(postCode)) {
    dailyTaskList.value.splice(0, 1);
  }
  if (["10005", "10007"].includes(postCode)) {
    dailyTaskList.value.splice(1, 1);
  }
  if (["10007"].includes(postCode)) {
    dailyTaskList.value.splice(2, 1);
  }
});
onLoad(() => {
  const timestamp = Date.now();
  currentTime.value = formatTimestamp(timestamp);
  safeAreaInsetsTop.value = getSafeAreaInsetsTop();
});
const visitTime = (type) => {
  // 1 终端拜访,2市场协访,3代理商拜访
  let parmas = {
    createDate: currentTime.value,
    personId: salesmanId,
    visitType:
      type === "terminalVisit" ? "1" : type === "marketVisit" ? "2" : "3",
  };
  getByCurrentTime(parmas).then((res) => {
    const { code, data } = res;
    if (code == 0 && data) {
      visitStore.setSiginType(2);
      visitStore.setNeedRefresh(true);
      visitStore.setIsIndex(true);
      visitStore.setClientShop({
        id: data.purMerchantId,
        name: data.purMerchantName,
        address: data.destination,
        latitude: data.purMerchantLatitude,
        longitude: data.purMerchantLongitude,
        userId: data.coachedPersonId,
        userName: data.coachedPersonName,
        agentId: data.agentEmployeeId,
        agentName: data.agentEmployeeName,
        createDate: data.createDate,
        locationList: null,
      });
      visitStore.setClientDept({
        departmentId: data.baseHospitalId,
        departmentName: data.baseHospitalName,
      });
      visitStore.setClientDoctor({
        ...visitStore.$state.clientDoctor,
        name: data.doctorName,
        id: data.doctorId,
        init: true,
      });
      visitStore.setAgent({
        agentEmployeeId: data.agentEmployeeId,
        agentEmployeeName: data.agentEmployeeName,
        agentId: data.agentId,
        agentName: data.agentName,
        isAgent: data.agentIsPresent,
      });
      visitStore.setAgentEmployee({
        personName: data.coachedPersonName,
        departNames: data.departNames,
        isScene: data.crmIsPresent,
        personId: data.coachedPersonId,
        id: data.id,
        isEvaluate: data?.isEvaluate,
        personCode: data.coachedErpCode,
      });
      uni.reLaunch({
        url: `/pages/subVisit/createVisit/createVisit`,
      });
    } else {
      if (type === "terminalVisit") {
        uni.navigateTo({
          url: `/pages/subVisit/selectClientShop/selectClientShop`,
        });
      }
      if (type === "agentVisit") {
        uni.navigateTo({
          url: `/pages/subVisit/selectAgent/selectAgent`,
        });
      }
      if (type === "marketVisit") {
        uni.navigateTo({
          url: `/pages/subVisit/selectSub/selectSub`,
        });
      }
    }
  });
};
const triggerDailyTask = (type) => {
  let url = "";
  switch (type) {
    case "visit":
      url = "/pages/subReport/reportList/reportList?pageType=plan";
      break;
    case "meeting":
      url = "/pages/subMeeting/meetingList/meetingList?meetingType=academic";
      break;
    case "terminalDev":
      url = "/pages/subTerminal/terminalList/terminalList";
      break;
    case "terminalVisit":
      visitTime(type);
      visitStore.setVisitType("1");
      break;
    case "marketVisit":
      visitTime(type);
      visitStore.setVisitType("2");
      break;
    case "agentVisit":
      visitTime(type);
      visitStore.setVisitType("3");
      break;
    case "agentDA":
      url = "/subPackage/agentDA/index";
      break;
    case "agentPG":
      url = "/subPackage/agentPG/index";
      break;
  }
  url &&
    uni.navigateTo({
      url: url,
    });
};
const triggerTeamTask = (type) => {
  if (type === "teamReport") {
    // pageType=report 工作报告
    // pageType=plan 工作计划
    uni.navigateTo({
      url: `/pages/subReport/reportList/reportList?pageType=report`,
    });
  } else if (type === "visit") {
    uni.navigateTo({
      url: `/pages/subReport/reportList/reportList?pageType=plan`,
    });
  } else {
    // meetingType=team 团队会议
    // meetingType=academic 学术会议
    uni.navigateTo({
      url: `/pages/subMeeting/meetingList/meetingList?meetingType=team`,
    });
  }
};
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";

.workbenches-page {
  .page-content {
    @include globalPageStyle();

    .work-panel {
      padding: 28rpx 20rpx;
      box-sizing: border-box;

      .dailyTask,
      .teamTask {
        width: 100%;
        @include cardBgCommonStyle();
        padding: 16rpx 24rpx 24rpx;

        .title {
          @include setBoldFont(32rpx, 48rpx, #2f3133);
          margin-left: 8rpx;
        }

        .content {
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;

          .item {
            color: var(--text-color);
            width: 320rpx;
            height: 140rpx;
            margin-top: 20rpx;
            // background: pink; //这个颜色要换成图片
            display: flex;
            align-items: center;
            justify-content: flex-start;
            box-sizing: border-box;
            // // padding-left: 12rpx;
            > image {
              width: 100%;
              height: 100%;
            }
          }
        }
      }

      .teamTask {
        margin-top: 28rpx;
      }
    }
  }
}
</style>
