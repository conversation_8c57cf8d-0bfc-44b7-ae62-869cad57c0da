<template>
	<view class="meetList-panel">
		<view class="page-content">
			<view class="bottom-panel">
				<!-- 会议信息 -->
				<view class="meeting-info" v-for="(group, groupKey) in allMeetingData" :key="groupKey">
          <view v-for="(item, key) in group">
            <van-cell
                v-if="!group[key].hidden"
                :class="{ 'required': key !== 'meetingDetails' }"
                :key="key"
                :is-link="item.isLink && !isInputField(key)"
                :title="item.title"
                @tap="!isInputField(key) && triggerCell(key, item)"
            >
              <view style="position: relative" v-if="isInputField(key) && (status ==0 || status == 2 || status == null)">
                <!-- 如果是输入框项（如 会议主题、预算费用、参会人数），显示输入框 -->
                <input  v-model="item.value" placeholder="请输入" class="meeting-input" maxlength="30" />
              </view>
              <!-- 否则显示文本 -->
              <text v-else>{{ item.specialLabel || item.label || item.value }}</text>
              <view  v-if="key === 'meetingDetails'" style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%)">
                <image   @click.stop="deteleFile" :data-value="item" src="../static/icon_close.png" class="delete-icon"></image>
              </view>
            </van-cell>
          </view>

				</view>

				<!-- 提交按钮 -->
				<view v-if="status == 0 || status == 2 || status == null" class="submit-button" @click="handlSubmitClick">提交</view>
			</view>
		</view>
		<p-tabbar tabbarSelect="work" />
    <uv-datetime-picker ref="datetimePicker" v-model="datetimePickerValue" :minDate="minDate" mode="datetime" @confirm="datePickerConfirm"></uv-datetime-picker>
	</view>
</template>

<script setup>
import {onLoad} from '@dcloudio/uni-app';
import {computed, ref, watch} from 'vue';
import dayjs from '@/uni_modules/uv-ui-tools/libs/util/dayjs.js'
import {uploadPic} from "@/common/api/file";
import {
  academicconferencesinfo,
  academicconferencesinfoAdd,
  academicconferencesinfoUpdate,
  bdmteamconferencesinfoAdd,
  bdmteamconferencesinfoInfo, bdmteamconferencesinfoUpdate
} from "@/common/api/meeting";
import {dictionaryDetail} from "@/common/api/business";
import {useVisitStore} from "@/common/store/visit";

const datetimePickerValue = ref(null)
const datetimePicker = ref(null)

const minDate = ref(new Date())
const id = ref(null)

const customStyle = {
	fontSize: '38rpx',
	color: '#303030',
	fontWeight: 'normal',
	background: 'transparent'
};

const meetingOptions = {
	meetingType: [
		{ label: '终端会', value: '1' },
		{ label: '品牌会', value: '2' }
	],
  meetingMode:[
      { label: '线上', value: '1' },
      { label: '线下', value: '2' }
  ],
	meetingCategory: {
		'1': [
			{ label: '主任科会&圆桌会', value: '1' },
			{ label: '外部培训', value: '2' },
			{ label: '口服线文章征集', value: '3' }
		],
		'2': [
			{ label: '走进康缘', value: '1' },
			{ label: '省市年会', value: '2' },
			{ label: '城市沙龙会', value: '3' },
			{ label: '专家研讨会', value: '4' },
			{ label: 'VIP研讨会', value: '5' },
			{ label: 'MDT会议', value: '6' }
		]
	},
	meetingAccount: [],
  teamMeetingAccount: []
};

const steps = [
	{ text: '地区主管', desc: '赵子龙' },
	{ text: '省区主管', desc: '艾德' }
];
const status = ref(null)

const currentTime = ref('');
const meetingInfo = ref({});
const meetingBudget = ref({});
const meetingAttendees = ref({});
const meetingTimeLocation = ref({});

const allMeetingData = computed(() => ({
	meetingInfo: meetingInfo.value,
	meetingBudget: meetingBudget.value,
	meetingAttendees: meetingAttendees.value,
	meetingTimeLocation: meetingTimeLocation.value
}));

const deteleFile = () => {
  uni.showModal({
    title: '提示',
    content: '确定删除该文件吗？',
    success: (res) => {
      if (res.confirm) {
        meetingBudget.value.meetingDetails = {
          ...meetingBudget.value.meetingDetails,
          value: null,
          label: null
        }
      }
    }
  });
}

const isInputField = (key) => ['meetingTheme', 'meetingCost', 'meetingPeople', 'meetingLink'].includes(key);

// meetingType=team 团队会议
// meetingType=academic 学术会议
const meetingType = ref('');

const selectProduct = (selection) => {
  if(!selection || selection.length === 0) {
    meetingInfo.value.meetingProduct = {
      ...meetingInfo.value.meetingProduct,
      label: null,
      value: null
    }
    return
  }
  meetingInfo.value.meetingProduct = {
    ...meetingInfo.value.meetingProduct,
    label: selection.map((item) => item.label).join('\n'),
    value: selection.map((item) => item.value).join(',')
  }
}

const selectTerminal = (selection) => {
  if(!selection || selection.length === 0) {
    meetingAttendees.value.meetingTerminal = {
      ...meetingAttendees.value.meetingTerminal,
      label: null,
      specialLabel: null,
      value: null
    }
    return
  }
  meetingAttendees.value.meetingTerminal = {
    ...meetingAttendees.value.meetingTerminal,
    label: selection.map((item) => item.name).join('\n'),
    specialLabel: selection.length > 1 ? `已选${selection.length}家` : selection[0].name,
    value: selection.map((item) => item.id).join(',')
  }
}

const selectSpeaker = (selection) => {
  if(!selection || selection.length === 0) {
    meetingAttendees.value.meetingSpeaker = {
      ...meetingAttendees.value.meetingSpeaker,
      label: null,
      value: null
    }
    return
  }
  meetingAttendees.value.meetingSpeaker = {
    ...meetingAttendees.value.meetingSpeaker,
    label: selection.map((item) => item.name).join('\n'),
    value: selection.map((item) => item.id).join(',')
  }
}

const selectDept = (selection) => {
  if(!selection || selection.length === 0) {
    meetingAttendees.value.meetingDepartment = {
      ...meetingAttendees.value.meetingDepartment,
      label: null,
      value: null
    }
    return
  }
  meetingAttendees.value.meetingDepartment = {
    ...meetingAttendees.value.meetingDepartment,
    label: selection.map((item) => item.text).join('\n'),
    value: selection.map((item) => item.id).join(',')
  }
}

const selectResponsiblePerson = (selection) => {
  if(!selection || selection.length === 0) {
    if(meetingType.value === 'academic') {
      meetingTimeLocation.value.meetingHead = {
        ...meetingTimeLocation.value.meetingHead,
        label: null,
        value: null
      }
    } else {
      meetingInfo.value.meetingHead = {
        ...meetingInfo.value.meetingHead,
        label: null,
        value: null
      }
    }
    return
  }
  if(meetingType.value === 'academic') {
    meetingTimeLocation.value.meetingHead = {
      ...meetingTimeLocation.value.meetingHead,
      label: selection.map((item) => item.name).join('\n'),
      value: selection.map((item) => item.userId).join(',')
    }
  } else {
    meetingInfo.value.meetingHead = {
      ...meetingInfo.value.meetingHead,
      label: selection.map((item) => item.name).join('\n'),
      value: selection.map((item) => item.userId).join(',')
    }
  }
}

const datePickerConfirm = ({ value }) => {
  const date = dayjs(value).format("YYYY-MM-DD HH:mm")
  if(meetingType.value === 'academic') {
    meetingTimeLocation.value.meetingTime = {
      ...meetingTimeLocation.value.meetingTime,
      label: date,
      value:date
    }
  } else {
    meetingInfo.value.meetingTime = {
      ...meetingInfo.value.meetingTime,
      label: date,
      value:date
    }
  }

}

const addressConfirm = ({ address, name }) => {
  if(meetingType.value === 'academic') {
    meetingTimeLocation.value.meetingAddress = {
      ...meetingTimeLocation.value.meetingAddress,
      label: `${address}${name}`,
      value:`${address}${name}`
    }
  } else {
    meetingInfo.value.meetingAddress = {
      ...meetingInfo.value.meetingAddress,
      label: `${address}${name}`,
      value:`${address}${name}`
    }
  }

}

const chooseMessageFileSuccess = ({tempFiles}) => {
  uploadPic(tempFiles[0].path).then(res => {
    const result = JSON.parse(res)
    meetingBudget.value.meetingDetails = {
      ...meetingBudget.value.meetingDetails,
      label: tempFiles[0].name,
      value: result.data[0].fileUrl
    }
  })
}

const selectMeetingPeople = (selection) => {
  meetingInfo.value.meetingAttendees = {
    ...meetingInfo.value.meetingAttendees,
    label: selection.map((item) => item.name).join('\n'),
    value: selection.map((item) => item.userId).join(',')
  }
}

defineExpose({
  selectProduct: selectProduct,
  selectTerminal: selectTerminal,
  selectSpeaker: selectSpeaker,
  selectResponsiblePerson: selectResponsiblePerson,
  selectDept: selectDept,
  selectMeetingPeople: selectMeetingPeople
})



onLoad((option) => {

	meetingType.value = option.meetingType || 'academic';
	const defaultData = meetingType.value === 'academic' ? academicMeetingData() : teamMeetingData();
	Object.assign(meetingInfo.value, defaultData.meetingInfo);
	Object.assign(meetingBudget.value, defaultData.meetingBudget);
	Object.assign(meetingAttendees.value, defaultData.meetingAttendees);
	Object.assign(meetingTimeLocation.value, defaultData.meetingTimeLocation);



  if(meetingType.value === 'team') {
    // 团队会议类型
    dictionaryDetail({itemId: '1888119167575261185'}).then(res => {
      meetingOptions.meetingType = res.data.map(item =>({ value: item.value, label: item.name }))
    })
    // 团队预算科目
    dictionaryDetail({itemId: '1888120050409144322'}).then(res => {
      meetingOptions.teamMeetingAccount = res.data.map(item =>({ value: item.value, label: item.name }))
    })

  } else {
    dictionaryDetail({itemId: '1887763733270122498'}).then(res => {
      meetingOptions.meetingAccount = res.data.map(item =>({ value: item.value, label: item.name }))
    })
  }

  id.value = option.id
  if(meetingType.value === 'academic' && option.id) {
    // 学术会议类型
    academicconferencesinfo({ id: option.id }).then(res => {
      meetingInfo.value.meetingCategory.value = res.data.actCategory;
      meetingInfo.value.meetingCategory.label = res.data.actCategoryname;
      meetingInfo.value.meetingType.value = res.data.actType;
      meetingInfo.value.meetingType.label = res.data.acttypeName;
      meetingBudget.value.meetingAccount.value = res.data.budgetAccount;
      meetingBudget.value.meetingAccount.label = res.data.budgetAccountName;
      meetingBudget.value.meetingCost.value = res.data.estimatedCost;
      meetingBudget.value.meetingDetails.value = res.data.fileId;
      meetingBudget.value.meetingDetails.label = res.data.fileName;
      meetingAttendees.value.meetingDepartment.value = res.data.hosDepartId;
      meetingAttendees.value.meetingDepartment.label = res.data.hosDepartName;
      meetingInfo.value.meetingTheme.value = res.data.meetTheme;
      meetingTimeLocation.value.meetingAddress.value = res.data.meetingAdress;
      meetingTimeLocation.value.meetingAddress.label = res.data.meetingAdress;
      meetingTimeLocation.value.meetingTime.value = res.data.meetingTime;
      meetingTimeLocation.value.meetingTime.label = res.data.meetingTime;
      if(meetingAttendees.value.meetingPeople) {
        meetingAttendees.value.meetingPeople.value = res.data.peopleNum;
      }
      meetingTimeLocation.value.meetingHead.value = res.data.personChargeId;
      meetingTimeLocation.value.meetingHead.label = res.data.personChargeName;
      meetingInfo.value.meetingProduct.value = res.data.productId;
      meetingInfo.value.meetingProduct.label = res.data.productName;
      meetingAttendees.value.meetingSpeaker.value = res.data.speakerId;
      meetingAttendees.value.meetingSpeaker.label = res.data.speakerName;
      meetingAttendees.value.meetingTerminal.value = res.data.terminalId;
      meetingAttendees.value.meetingTerminal.label = res.data.terminalName;
      console.log(meetingTimeLocation)
      status.value = res.data.status
    })
  }
  if(meetingType.value === 'team' && option.id) {
    bdmteamconferencesinfoInfo({ id: option.id }).then(res => {
      meetingInfo.value.meetingType.value = res.data.teamMeetingType;
      meetingInfo.value.meetingType.label = res.data.teamMeetingName;
      meetingInfo.value.meetingTheme.label = res.data.meetName;
      meetingInfo.value.meetingTheme.value = res.data.meetName;
      meetingBudget.value.meetingAccount.value = res.data.teamAccount;
      meetingBudget.value.meetingAccount.label = res.data.teambudgetAccountName;
      meetingBudget.value.meetingCost.value = res.data.estimatedCost;
      meetingBudget.value.meetingDetails.value = res.data.fileId;
      meetingBudget.value.meetingDetails.label = res.data.fileName;
      meetingInfo.value.meetingAddress.value = res.data.meetingAddress;
      meetingInfo.value.meetingAddress.label = res.data.meetingAddress;
      meetingInfo.value.meetingLink.value = res.data.meetingConnect;
      meetingInfo.value.meetingMode.value = res.data.meetingModeType;
      meetingInfo.value.meetingMode.label = res.data.meetingModeName;
      meetingInfo.value.meetingTime.value = res.data.teamMeetingTime;
      meetingInfo.value.meetingTime.label = res.data.teamMeetingTime;
      meetingInfo.value.meetingHead.value = res.data.personChargeId;
      meetingInfo.value.meetingHead.label = res.data.personChargeName;
      meetingInfo.value.meetingAttendees.value = res.data.userIds;
      meetingInfo.value.meetingAttendees.label = res.data.userNames.split(',').join('\n');
      status.value = res.data.status
    })
  }
})


const academicMeetingData = () => ({
	meetingInfo: {
		meetingType: { title: '活动类型', value: '', label: '', isLink: true },
		meetingCategory: { title: '活动类别', value: '', label: '', isLink: true },
		meetingTheme: { title: '会议主题', value: '', label: '', isLink: false },
		meetingProduct: { title: '会议产品', value: '', label: '', isLink: true }
	},
	meetingBudget: {
		meetingAccount: { title: '预算科目', value: '', label: '', isLink: true },
		meetingCost: { title: '预算费用', value: '', label: '', isLink: false },
		meetingDetails: { title: '预算明细', value: '', label: '', isLink: false }
	},
	meetingAttendees: {
		meetingTerminal: { title: '参会终端', value: '', label: '', isLink: true },
		meetingPeople: { title: '参会人数', value: '', label: '', isLink: false },
		meetingDepartment: { title: '科室', value: '', label: '', isLink: true },
		meetingSpeaker: { title: '讲者', value: '', label: '', isLink: true }
	},
	meetingTimeLocation: {
		meetingHead: { title: '负责人', value: '', label: '', isLink: true },
		meetingTime: { title: '召开时间', value: '', label: '', isLink: true },
		meetingAddress: { title: '召开地址', value: '', label: '', isLink: true }
	}
});

const teamMeetingData = () => ({
	meetingInfo: {
		meetingType: { title: '会议类型', value: '', label: '', isLink: true },
    meetingTheme: { title: '会议名称', value: '', label: '', isLink: false },
		meetingTime: { title: '会议时间', value: '', label: '', isLink: true },
		meetingHead: { title: '负责人', value: '', label: '', isLink: true },
		meetingAttendees: { title: '参会人', value: '', label: '', isLink: true },
		meetingMode: { title: '会议模式', value: '', label: '', isLink: true },
		meetingAddress: { title: '会议地址', value: '', label: '', isLink: true },
		meetingLink: { title: '会议链接', value: '', label: '', isLink: false }
	},
	meetingBudget: {
		meetingAccount: { title: '预算科目', value: '', label: '', isLink: true },
		meetingCost: { title: '预算费用', value: '', label: '', isLink: false },
		meetingDetails: { title: '预算明细', value: '', label: '', isLink: false }
	}
});

watch(() =>  meetingInfo.value?.meetingMode?.value, (mode) => {
  if(mode == '1') {
    meetingInfo.value.meetingAddress.hidden = true
    meetingInfo.value.meetingLink.hidden = false
  }
  if(mode == '2') {
    meetingInfo.value.meetingAddress.hidden = false
    meetingInfo.value.meetingLink.hidden = true
  }
})

const navigateTo = (url) => {
  uni.navigateTo({ url });
  const visitStore = useVisitStore()
  visitStore.setSelectedProductIds(meetingInfo.value?.meetingProduct?.value?.split(',') || [])
  visitStore.setSelectedTerminalIds(meetingAttendees.value?.meetingTerminal?.value?.split(',') || [])
  visitStore.setSelectedDeptIds(meetingAttendees.value?.meetingDepartment?.value?.split(',') || [])
  visitStore.setSelectedSpeakerIds(meetingAttendees.value?.meetingSpeaker?.value?.split(',') || [])
  if(meetingType.value === 'academic') {
    visitStore.setSelectedResponsiblePersonIds(meetingTimeLocation.value?.meetingHead?.value?.split(',') || [])
  } else {
    visitStore.setSelectedResponsiblePersonIds(meetingInfo.value?.meetingHead?.value?.split(',') || [])
  }
};

const triggerCell = (key, item) => {
	console.log(`点击了 ${item.title}，key：${key}，值：${item.value}`);
  if(!(status.value == null || status.value ==0 || status.value == 2)) {
    return
  }
	const actionMapping = {
		meetingType: showActionSheet.bind(null, meetingOptions.meetingType, 'meetingType', meetingInfo.value),
    meetingMode: showActionSheet.bind(null, meetingOptions.meetingMode, 'meetingMode', meetingInfo.value),
		meetingCategory: showCategoryActionSheet,
		meetingAccount: showActionSheet.bind(null, meetingType.value === 'academic' ? meetingOptions.meetingAccount: meetingOptions.teamMeetingAccount, 'meetingAccount', meetingBudget.value),
    meetingDepartment: navigateTo.bind(null, `/pages/subMeeting/selectDepartment/selectDepartment?id=${meetingAttendees.value.meetingTerminal?.value}`),
    meetingSpeaker: navigateTo.bind(null, `/pages/subMeeting/selectSpeaker/selectSpeaker?id=${meetingAttendees.value.meetingTerminal?.value}`),
    meetingProduct: navigateTo.bind(null, `/pages/subMeeting/meetingProduct/meetingProduct`),
    meetingTerminal: navigateTo.bind(null, `/pages/subMeeting/selectTerminal/selectTerminal`),
    meetingHead: navigateTo.bind(null, `/pages/subMeeting/selectResponsiblePerson/selectResponsiblePerson`),
    meetingTime: () => { minDate.value = new Date(), datetimePicker.value.open(); },
    meetingAddress: () => { uni.chooseLocation({success: addressConfirm}); },
    meetingDetails: () => { uni.chooseMessageFile({success: chooseMessageFileSuccess, count: 1}); },
    meetingAttendees: navigateTo.bind(null, `/pages/subMeeting/selectMeetingPeople/selectMeetingPeople`)
	};

	const action = actionMapping[key] || defaultAction;
	action(item);
};

const showActionSheet = (options, fieldName, meetingInfoRef) => {

	uni.showActionSheet({
		itemList: options.map((o) => o.label),
		success: (res) => {
      if(fieldName === 'meetingType' && meetingInfoRef.meetingCategory) {
        meetingInfoRef.meetingCategory.value = null;
        meetingInfoRef.meetingCategory.label = null;
      }
			const selectedOption = options[res.tapIndex];
      meetingInfoRef[fieldName] = { ...meetingInfoRef[fieldName], ...selectedOption };
		}
	});
};




const showCategoryActionSheet = (item) => {
	if (!meetingInfo.value.meetingType.value) {
		uni.showToast({ title: '请先选择活动类型', icon: 'none' });
		return;
	}
	const categoryOptions = meetingOptions.meetingCategory[meetingInfo.value.meetingType.value] || [];
	showActionSheet(categoryOptions, 'meetingCategory', meetingInfo.value);
};

const defaultAction = (item) => {
	uni.showToast({ title: `点击了 ${item.title}`, icon: 'none' });
};

function numMulti(num1, num2) {
  var baseNum = 0;
  try {
    baseNum += num1.toString().split(".")[1].length;
  } catch (e) {
  }
  try {
    baseNum += num2.toString().split(".")[1].length;
  } catch (e) {
  }
  return Number(num1.toString().replace(".", "")) * Number(num2.toString().replace(".", "")) / Math.pow(10, baseNum);
};
const handlSubmitClick = () => {
  console.log('提交数据', allMeetingData.value);

  Object.keys(allMeetingData.value).forEach(key => {
    const item = allMeetingData.value[key];
    Object.keys(item).forEach(childKey => {
      const childItem = item[childKey];
      if(childKey === 'meetingAddress' || childKey === 'meetingLink') {
        if (childKey === 'meetingAddress') {
          if (!childItem.value && item.meetingMode.value == '2') {
            uni.showToast({ title: `${childItem.title}不能为空`, icon: 'none' });
            throw new Error(`${childItem.title}不能为空`)
          }
        }
        if (childKey === 'meetingLink') {
          if (!childItem.value && item.meetingMode.value == '1') {
            uni.showToast({ title: `${childItem.title}不能为空`, icon: 'none' });
            throw new Error(`${childItem.title}不能为空`)
          }
        }
      } else {
        if (!childItem.value && childKey !== 'meetingDetails') {
          uni.showToast({ title: `${childItem.title}不能为空`, icon: 'none' });
          throw new Error(`${childItem.title}不能为空`)
        }
      }
    })
  })

  if(meetingType.value === 'academic') {
    const api = id.value ? academicconferencesinfoUpdate : academicconferencesinfoAdd;
    api({
      id:id.value,
      actCategory: meetingInfo.value.meetingCategory.value,
      actCategoryname: meetingInfo.value.meetingCategory.label,
      actType: meetingInfo.value.meetingType.value,
      acttypeName: meetingInfo.value.meetingType.label,
      budgetAccount: meetingBudget.value.meetingAccount.value,
      budgetAccountName: meetingBudget.value.meetingAccount.label,
      estimatedCost: numMulti(meetingBudget.value.meetingCost.value, 100),
      fileId: meetingBudget.value.meetingDetails.value,
      fileName: meetingBudget.value.meetingDetails.label,
      hosDepartId: meetingAttendees.value.meetingDepartment.value,
      hosDepartName: meetingAttendees.value.meetingDepartment.label,
      meetTheme: meetingInfo.value.meetingTheme.value,
      meetingAdress: meetingTimeLocation.value.meetingAddress.value,
      meetingTime: meetingTimeLocation.value.meetingTime.value,
      peopleNum: meetingAttendees.value?.meetingPeople?.value,
      // personChargeCode:
      personChargeId: meetingTimeLocation.value.meetingHead.value,
      personChargeName: meetingTimeLocation.value.meetingHead.label,
      productId: meetingInfo.value.meetingProduct.value,
      productName: meetingInfo.value.meetingProduct.label,
      speakerId: meetingAttendees.value.meetingSpeaker.value,
      speakerName: meetingAttendees.value.meetingSpeaker.label,
      status: 0,
      terminalId: meetingAttendees.value.meetingTerminal.value,
      terminalName: meetingAttendees.value.meetingTerminal.label
    }).then(res => {
      if (res.code === 0) {
        uni.showToast({ title: '提交成功', icon: 'success', duration: 2000 });
        handleRefresh()
      } else {
        uni.showToast({ title: '提交失败', icon: 'none' });
      }
    })
  } else {
    const api = id.value ? bdmteamconferencesinfoUpdate : bdmteamconferencesinfoAdd;
    api({
      id:id.value,
      teamMeetingType: meetingInfo.value.meetingType.value,
      teamMeetingName: meetingInfo.value.meetingType.label,
      teamAccount: meetingBudget.value.meetingAccount.value,
      teambudgetAccountName: meetingBudget.value.meetingAccount.label,
      estimatedCost: numMulti(meetingBudget.value.meetingCost.value, 100),
      fileId: meetingBudget.value.meetingDetails.value,
      fileName: meetingBudget.value.meetingDetails.label,
      meetingAddress: meetingInfo.value.meetingAddress.value,
      meetingConnect: meetingInfo.value.meetingLink.value,
      meetingModeType: meetingInfo.value.meetingMode.value,
      meetingModeName: meetingInfo.value.meetingMode.label,
      teamMeetingTime: meetingInfo.value.meetingTime.value,
      personChargeId: meetingInfo.value.meetingHead.value,
      personChargeName: meetingInfo.value.meetingHead.label,
      meetingName: meetingInfo.value.meetingTheme.value,
      meetName: meetingInfo.value.meetingTheme.value,
      status: 0,
      userIds: meetingInfo.value.meetingAttendees.value,
      userNames: meetingInfo.value.meetingAttendees.label.split('\n').join(','),
    }).then(res => {
      if (res.code === 0) {
        uni.showToast({ title: '提交成功', icon: 'success', duration: 2000 });
        handleRefresh()
      } else {
        uni.showToast({ title: '提交失败', icon: 'none' });
      }
    })
  }

}

const handleRefresh = () => {
  const pages = getCurrentPages();
  const prePage = pages[pages.length - 2];
  prePage.$vm.refresh()
  setTimeout(() => {
    uni.navigateBack();
  }, 2000)
}

</script>

<style scoped lang="scss">
@import '@/static/scss/global.scss';

.meetList-panel {
	.page-content {
		@include globalPageStyle();
		display: flex;
		flex-direction: column;
		position: relative;
	}
  .delete-icon {
    width: 14px;
    height: 14px;
  }
	.bottom-panel {
		flex: 1;
		height: 0;
		box-sizing: border-box;
		padding: 28rpx 20rpx;
		overflow-y: scroll;
		.meeting-info {
			@include cardBgCommonStyle();
			box-sizing: border-box;
			padding: 0 8rpx;
			margin-bottom: 24rpx;

      .required {
        :deep(.van-cell__title) {
          &::before {
            content: '*';
            @include setBoldFont(28rpx, 44rpx, #f00);
          }
        }
      }
			:deep(.van-cell) {
				--cell-vertical-padding: 20rpx;
				--cell-horizontal-padding: 28rpx;
				--cell-background-color: transparent;
				border-bottom: 2rpx solid #ced4db;

				.van-cell__value {
					@include setlightFont(28rpx, 44rpx, #8d9094);
				}
				.meeting-theme-input {
					@include setlightFont(28rpx, 44rpx, #8d9094);
				}
			}
			/* 让最后一个 van-cell 去掉 border */
			:deep(.van-cell:last-child) {
				border-bottom: none;
			}
		}

		.steps-panel {
			@include cardBgCommonStyle();
			padding: 20rpx 28rpx;
			.title {
				@include setBoldFont(28rpx, 44rpx, #2f3133);
			}
		}
		.submit-button {
			@include commonButtonStyle();
			margin-top: 76rpx;
		}
	}
}
</style>
