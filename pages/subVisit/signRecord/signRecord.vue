<template>
  <view>
    <uv-loading-page
      :loading="loadingPage"
      loading-mode="spinner"
      loading-text="加载中..."
    ></uv-loading-page>
    <view class="signRecord-panel">
      <view class="top-panel">
        <view class="calendar-panel">
          <week-fold-calendar
            @change="change"
            allow-future
            :custom-style="customStyle"
            active-color="#fff"
            :min-date="minDate"
            activeBgColor="linear-gradient(180deg, #52ACFF -12%, #263AF0 117%)"
          ></week-fold-calendar>
          <view class="total-tips">
            <text class="name">打卡家次:</text>
            <text class="num">{{ signRecordList?.length || 0 }}</text>
          </view>
        </view>
      </view>
      <view class="menu-panel">
        <view class="menu-content">
          <view
            class="menu-item"
            :class="{
              current: currentMenu == index,
              last: index == menuList.length - 1,
            }"
            v-for="(item, index) in menuList"
            :key="index"
            @tap="onMenuChange"
            :data-value="index"
          >
            {{ item }}
          </view>
        </view>
      </view>
      <view class="record-list-panel">
        <scroll-view
          class="button-groups"
          scroll-y="true"
          @scrolltolower="throttledLoadMore"
          lower-threshold="200"
        >
          <view class="list-content">
            <view
              class="list-item"
              v-for="(item, index) in signRecordList"
              :key="index"
            >
              <view class="client-name">
                <text class="name" v-if="visitType != '3'">{{
                  item.purMerchantName
                }}</text>
                <view class="log-entrance">
                  <text
                    v-if="visitType == 2"
                    class="visitLog evaluate"
                    @tap="openEvaluate"
                    :data-value="item"
                    >协访评价</text
                  >
                  <text
                    v-else
                    class="visitLog log"
                    @tap="openVisitLog"
                    :data-value="item"
                    >拜访日志</text
                  >
                </view>
              </view>
              <view
                v-if="item.destination && visitType != '3'"
                class="client-address"
                >{{ item.destination }}</view
              >
              <view v-if="visitType == 3">
                {{ item.agentName }}/{{ item.agentEmployeeName }}
              </view>
              <view
                class="assistDefenseTarget"
                v-if="item.personName && currentMenu == 1"
              >
                打卡人：
                <text class="duration">{{ item.personName }}</text>
              </view>
              <view class="assistDefenseTarget" v-if="visitType == 2">
                招商代表：
                <text class="duration" style="margin-right: 10rpx">{{
                  item.coachedPersonName
                }}</text>
                <text v-if="item.crmIsPresent == 1" style="color: #4068f5"
                  >在场</text
                ><text v-if="item.crmIsPresent == 0" style="color: #fc474c"
                  >缺席</text
                >
              </view>
              <view class="assistDefenseTarget" v-if="visitType != 3">
                代理商：
                <text class="duration" style="margin-right: 10rpx">{{
                  item.agentEmployeeName
                }}</text>
                <text v-if="item.agentIsPresent == 1" style="color: #4068f5"
                  >在场</text
                ><text v-if="item.agentIsPresent == 0" style="color: #fc474c"
                  >缺席</text
                >
              </view>
              <view class="time" v-if="item.bdmSignInWechatVo?.length == 2">
                在场时长：
                <text class="duration">{{ item.durationTime }}</text>
                分钟
              </view>
              <view
                style="margin-top: 20rpx; font-size: 24rpx"
                v-if="visitType == 3"
              >
                <text class="time">{{
                  formatTimestamp(item.createDate, "hh:ii:ss")
                }}</text>
                <text class="time" style="margin: 0 20rpx">签到</text>
                <text>{{ item.address }}</text>
              </view>
              <view class="sign-infos" v-else>
                <view
                  class="content"
                  :class="{ max: item.bdmSignInWechatVo?.length == 2 }"
                >
                  <view
                    :class="Voindex == 0 ? 'signIn-info' : 'signOut-info'"
                    v-for="(Voitem, Voindex) in item.bdmSignInWechatVo"
                    :key="Voindex"
                  >
                    <text class="time">{{
                      formatTimestamp(Voitem.createDate, "hh:ii:ss")
                    }}</text>
                    <view class="right-panel">
                      <view class="sign-desc-status">
                        <text class="desc">{{
                          Voindex == 0 ? "签到" : "签退"
                        }}</text>
                        <!-- 异常：2,正常：1 -->
                        <text
                          class="status"
                          :class="{
                            errorSituation: Voitem.exceptionStatus != 1,
                          }"
                        >
                          {{
                            Voitem.exceptionStatus == 1
                              ? "正常"
                              : "超范围打卡:" +
                                ((Voitem.offset >= 0
                                  ? Voitem.offset
                                  : -1 * Voitem.offset) || 0) +
                                "m"
                          }}
                        </text>
                      </view>
                      <view class="remark-text" v-if="visitType != 3">
                        备注:
                        <text class="text">{{
                          Voitem.superableScopeRemarks
                        }}</text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>

        <view class="loading-list-panel" v-if="loadingList">
          <uv-loading-icon></uv-loading-icon>
        </view>
      </view>

      <visitLog
        v-if="isShowVisitLog"
        :imageList="imageList"
        @closeVisitLog="closeVisitLog"
        :remarks="remarks"
        :visit-purpose="visitPurpose"
        :visit-sumup="visitSumup"
      ></visitLog>
      <evaluate
        v-if="isShowEvaluate"
        :idEvaluate="idEvaluate"
        @closeEvaluate="closeEvaluate"
      ></evaluate>
    </view>
    <view class="placeholder-view"></view>
    <p-visit></p-visit>
    <p-tabbar tabbarSelect="work"></p-tabbar>
  </view>
</template>

<script setup>
import { ref, nextTick } from "vue";
import WeekFoldCalendar from "@/components/week-fold-calendar/week-fold-calendar.vue";
import visitLog from "./components/visitLog.vue";
import evaluate from "./components/evaluate.vue";
import { onLoad, onUnload, onShow } from "@dcloudio/uni-app";
import { listByDate, coachedpage } from "@/common/api/sign/index.js";
import {
  getYearStartDate,
  formatTimestamp,
  isFutureDate,
  isKAManager,
} from "@/utils/utils.js";
import { useVisitStore } from "@/common/store/visit";

import { USER_INFO_KEY } from "/common/const/cache.js";
const UserInfo = uni.getStorageSync(USER_INFO_KEY); // 从缓存里拿用户信息
const salesmanId = UserInfo && UserInfo.id ? UserInfo.id : null; // 业务员 ID，确保不存在时为 null

const visitStore = useVisitStore();
const visitType = ref(visitStore.$state.visitType); //拜访类型
const loadingPage = ref(true); //页面加载loading
const loadingList = ref(false); //页面加载loading

// 分页接口相关
const pageSize = 15; //分页接口数据 	每页大小
const pageNumber = ref(1); //当前页
const totalPage = ref(0); //总页数

const minDate = getYearStartDate(); // 限制日历最小时间 (月份选择器，只显示本年的月份)
const currentTime = ref("");
const imageList = ref(null);
const isShowVisitLog = ref(false);
const isShowEvaluate = ref(false);
const idEvaluate = ref(null);

// 如果是领导的话需要有一个tab菜单
const menuList = ref([]);
const currentMenu = ref(0);
const customStyle = ref({
  fontSize: "38rpx",
  color: "#303030",
  fontWeight: "normal",
  background: "transparent",
});
const isManage = () => {
  return ["10005", "10006"].includes(UserInfo.posts[0].code);
};
const signRecordList = ref([]);
const remarks = ref("");
const visitPurpose = ref("");
const visitSumup = ref("");
const isCanChangeButton = ref(true);
onLoad((option) => {
  // visitStore.setActiveTab(1);
  menuList.value =
    visitType.value === "2"
      ? ["我的协访", "下属协访"]
      : ["我的拜访", "下属拜访"];
  if (isManage()) {
    menuList.value = visitType.value === "2" ? ["我的协访"] : ["我的拜访"];
  }
  if (visitType.value === "2" && UserInfo.posts[0].code === "10002") {
    menuList.value = ["我的协访"];
  }
  const timestamp = Date.now();
  currentTime.value = formatTimestamp(timestamp);
  listByDateFun(true);
});
const change = (date) => {
  currentTime.value = date;
  // listByDateFun(currentTime.value);
  pageNumber.value = 1;
  getClientList();
};
const loadCoachedpageFun = async () => {
  if (!totalPage.value || totalPage.value >= pageNumber.value) {
    // pageNumber.value = pageNumber.value + 1;
    const result = await coachedpageFun();
    loadingList.value = false;
    isCanChangeButton.value = true;
    const { code, data } = result;
    if (code === 0) {
      if (pageNumber.value === 1) {
        signRecordList.value = data.list;
        totalPage.value = data.totalPage;
      } else {
        signRecordList.value = signRecordList.value.concat(data.list);
      }
    }
  }
};
const coachedpageFun = () => {
  loadingList.value = true;
  isCanChangeButton.value = false;
  const { posts, departments } = UserInfo;
  let postId = posts[0].id;
  let departmentId = departments[0].id;
  let parmas = {
    createDate: currentTime.value,
    departmentId: departmentId,
    size: pageSize,
    limit: pageNumber.value,
    postId: postId,
    visitType: visitStore.$state.visitType,
  };
  return coachedpage(parmas)
    .then((res) => {
      return res; // 成功时返回结果
    })
    .catch((error) => {
      console.error("Error:", error);
      throw error; // 将错误抛出，供外部捕获
    });
};
// 获取拜访日志
const listByDateFun = (isInit = false) => {
  loadingList.value = true;
  isCanChangeButton.value = false;
  signRecordList.value = [];
  let parmas = {
    createDate: currentTime.value,
    personId: salesmanId,
    visitType: visitStore.$state.visitType,
  };

  listByDate(parmas).then((res) => {
    const { code, data, msg } = res;
    if (code == 0) {
      signRecordList.value = data;
    } else {
      uni.showToast({
        title: msg,
        icon: "none",
        duration: 2000,
      });
    }
    isCanChangeButton.value = true;
    loadingList.value = false;
    if (isInit) {
      loadingPage.value = false;
    }
  });
};
const closeVisitLog = () => {
  isShowVisitLog.value = false;
  imageList.value = null;
  remarks.value = "";
  visitPurpose.value = "";
  visitSumup.value = "";
};
const closeEvaluate = () => {
  isShowEvaluate.value = false;
  idEvaluate.value = "";
};
const openEvaluate = (event) => {
  isShowEvaluate.value = false;
  nextTick(() => {
    console.log(
      event.currentTarget.dataset.value,
      "----  event.currentTarget.dataset.value"
    );
    const { signId } = event.currentTarget.dataset.value;
    isShowEvaluate.value = true;
    idEvaluate.value = signId;
  });
};
const openVisitLog = (event) => {
  let selectItem = event.currentTarget.dataset.value;
  if (visitType.value == 3) {
    if (selectItem.bdmSignInWechatVo[0].imageIds) {
      imageList.value = selectItem.bdmSignInWechatVo[0].imageIds.split(",");
      remarks.value = selectItem.bdmSignInWechatVo[0].remarks;
      visitPurpose.value = selectItem.bdmSignInWechatVo[0].visitPurpose;
      visitSumup.value = selectItem.bdmSignInWechatVo[0].visitSumup;
      isShowVisitLog.value = true;
    } else {
      uni.showToast({
        title: "暂无拜访日志，请填完后再查看",
        icon: "none",
        duration: 2000,
      });
    }
  }
  if (visitType.value == 1) {
    if (selectItem.bdmSignInWechatVo.length >= 2) {
      imageList.value = selectItem.bdmSignInWechatVo[1].imageIds.split(",");
      remarks.value = selectItem.bdmSignInWechatVo[1].remarks;
      visitPurpose.value = selectItem.bdmSignInWechatVo[1].visitPurpose;
      visitSumup.value = selectItem.bdmSignInWechatVo[1].visitSumup;
      isShowVisitLog.value = true;
    } else {
      uni.showToast({
        title: "暂无拜访日志，请签退后再查看",
        icon: "none",
        duration: 2000,
      });
    }
  }
};
const onMenuChange = (event) => {
  if (!isCanChangeButton.value) {
    return;
  }
  currentMenu.value = event.currentTarget.dataset.value;
  getClientList();
};
// 获取打卡记录数据、已经区分了协访、拜访、下属
const getClientList = () => {
  signRecordList.value = [];
  if (isManage()) {
    listByDateFun();
  } else {
    currentMenu.value === 0 ? listByDateFun() : loadCoachedpageFun();
  }
};
const throttledLoadMore = () => {
  if (currentMenu.value === 0) return;
  if (loadingList.value) return;
  pageNumber.value++;
  loadCoachedpageFun();
};
// onUnload(() => {
//   visitStore.setActiveTab(0);
// });
onShow(() => {
  visitStore.setActiveTab(1);
});
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";
.button-groups {
  overflow: scroll;
  height: 100%;
  box-sizing: border-box;
}
.placeholder-view {
  width: 100%;
  height: 100rpx;
}
.signRecord-panel {
  width: 100%;
  box-sizing: border-box;
  height: calc(100vh - env(safe-area-inset-bottom) - 156rpx);
  display: flex;
  flex-direction: column;
  position: relative;
  .top-panel {
    .calendar-panel {
      position: relative;
      .total-tips {
        position: absolute;
        right: 46rpx;
        top: 0;
        height: 44rpx;
        display: flex;
        align-items: center;
        .name {
          @include setBoldFont(20rpx, 44rpx, #1d1d1d);
        }
        .num {
          @include setBoldFont(28rpx, 44rpx, #4068f5);
        }
      }
    }
  }
  .menu-panel {
    height: 68rpx;
    box-sizing: border-box;
    width: 1;
    padding: 0 44rpx;
    margin-top: 24rpx;
    box-sizing: border-box;
    .menu-content {
      @include setlightFont();
      color: #747776;
      display: flex;
      align-items: center;
      border-radius: 48rpx;
      background: rgba(255, 255, 255, 0.9);
      justify-content: center;
      /* 橙色投影 */
      height: 68rpx;
      box-shadow: 2rpx 2rpx 8rpx 0rpx #f3c66a;
    }
    .menu-item {
      margin-right: 78rpx;
      &.current {
        @include setBoldFont(28rpx, 36rpx, #303030);
        border-bottom: 4rpx solid #f4d79e;
      }
      &.last {
        margin: 0;
      }
    }
  }
  .record-list-panel {
    flex: 1;
    overflow-y: scroll;
    box-sizing: border-box;
    padding: 0 20rpx 28rpx;
    margin-top: 28rpx;
    .empty-step {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .loading-list-panel {
      height: 100%;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .list-content {
      .list-item {
        padding: 24rpx;
        margin-bottom: 28rpx;
        @include cardBgCommonStyle();
        .client-name {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          position: relative;
          .name {
            width: 80%;
            @include setBoldFont(28rpx, 36rpx, $uni-text-color);
          }
          .log-entrance {
            position: absolute;
            top: 0;
            right: 0;
            display: flex;
            flex-direction: column;
            .visitLog {
              display: flex;
              align-items: center;
              @include setlightFont(20rpx, 26rpx, #4068f5);
              &::before {
                content: "";
                width: 24rpx;
                height: 24rpx;
                display: inline-block;
                margin-right: 4rpx;
              }
              &.log {
                margin-bottom: 24rpx;
                &::before {
                  @include setBackgroundImage("../static/icon_log.png");
                }
              }
              &.evaluate {
                &::before {
                  @include setBackgroundImage("../static/icon_evaluate.png");
                }
              }
            }
          }
        }
        .client-address {
          @include setlightFont(24rpx, 32rpx, #bbbdbf);
          width: 540rpx;
          margin-top: 20rpx;
        }
        .assistDefenseTarget {
          margin-top: 20rpx;
          margin-bottom: 20rpx;
          @include setlightFont(24rpx, 32rpx, #bbbdbf);
          .duration {
            @include setBoldFont(24rpx, 32rpx, #1d1d1d);
          }
        }
        .time {
          @include setlightFont(24rpx, 32rpx, #bbbdbf);
          // margin-top: 20rpx;
          .duration {
            @include setBoldFont(28rpx, 32rpx, #4068f5);
          }
        }

        .sign-infos {
          margin-top: 40rpx;
          .content {
            position: relative;
            .signIn-info,
            .signOut-info {
              margin-right: 20rpx;
              @include setlightFont(24rpx, 32rpx, #bbbdbf);
              display: flex;
              justify-content: flex-start;
              align-items: flex-start;
              .time {
                width: 100rpx;
              }
              .right-panel {
                display: flex;
                flex-direction: column;
                margin-left: 20rpx;
                .sign-desc-status {
                  @include setlightFont(24rpx, 40rpx, #bbbdbf);
                  .status {
                    @include setBoldFont(24rpx, 32rpx, #303030);
                    margin-left: 28rpx;
                    &.errorSituation {
                      @include setBoldFont(24rpx, 32rpx, #e52121);
                    }
                  }
                }
                .remark-text {
                  @include setBoldFont(20rpx, 36rpx, #bbbdbf);
                  .text {
                    margin-left: 28rpx;
                    @include setBoldFont(20rpx, 36rpx, #e52121);
                  }
                }
              }
            }
            .signOut-info {
              margin-top: 32rpx;
            }
            &.max {
              &::before {
                content: "";
                width: 4rpx;
                height: 68rpx;
                border-radius: 22rpx;
                background: #d9d9d9;
                @include absoluteVerticalCenter();
                left: 46rpx;
                top: 70rpx;
              }
              &::after {
                content: "";
                width: 540rpx;
                height: 2rpx;
                border-radius: 22rpx;
                background: #d9d9d9;
                @include absoluteVerticalCenter();
                // left: 46rpx;
                right: 0;
              }
            }
          }
          .remark {
            @include setlightFont(20rpx, 26rpx, #e52121);
            margin-left: 120rpx;
            margin-top: 10rpx;
          }
        }
      }
    }
  }
}
</style>
