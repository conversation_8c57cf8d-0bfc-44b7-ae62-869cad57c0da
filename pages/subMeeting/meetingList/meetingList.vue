<template>
	<view class="meetList-panel">
		<view class="page-content">
			<view class="top-panel">
				<week-fold-calendar
					@change="change"
					allow-future
					:custom-style="customStyle"
					active-color="#fff"
					:min-date="minDate"
					activeBgColor="linear-gradient(180deg, #52ACFF -12%, #263AF0 117%)"
				></week-fold-calendar>
			</view>
			<view class="bottom-panel">
				<scroll-view class="list-panel" scroll-y="true" @scrolltolower="throttledLoadMore" lower-threshold="200">
					<view class="content-box">
            <template v-if="meetingType === 'academic'">
              <view class="client-item" v-for="(item, index) in list" :key="item.id" @tap="triggerClient(item)" :data-item="item">
                <!-- PENDING_APPROVAL: '审批中',
                REJECTED: '审批驳回',
                UPCOMING: '待开始',
                IN_PROGRESS: '进行中',
                COMPLETED: '已召开' -->
                <view class="meet-status PENDING_APPROVAL">{{ item.status }}</view>
                <view class="meet-lable">{{ item.actCategoryname }}</view>
                <view class="meet-name">{{ item.meetTheme }}</view>
                <view class="meet-info">
                  <text style="margin-right: 100rpx">
                    负责人：
                    <text class="stress-text">{{ item.personChargeName }}</text>
                  </text>
                  <text>
                    SPU：
                    <text class="stress-text">{{ item.productName }}</text>
                  </text>
                </view>

                <view class="meet-info">
                  <text style="margin-right: 70rpx">
                    参会终端：
                    <text class="stress-text">{{ item.terminalCount }}</text>
                  </text>
                  <text>
                    参会人数：
                    <text class="stress-text">{{ item.peopleNum }}</text>
                  </text>
                </view>
                <view class="meet-time">
                  召开时间：
                  <text class="stress-text">{{ item.meetingTime }}</text>
                </view>
              </view>
            </template>
            <template v-else>
              <view class="client-item" v-for="(item, index) in list" :key="item.id" @tap="triggerClient(item)" :data-item="item">
                <!-- PENDING_APPROVAL: '审批中',
                REJECTED: '审批驳回',
                UPCOMING: '待开始',
                IN_PROGRESS: '进行中',
                COMPLETED: '已召开' -->
                <view class="meet-status PENDING_APPROVAL">{{ item.status }}</view>
                <view class="meet-lable">{{ item.teamMeetingName }}</view>
                <view class="meet-name">{{ item.meetName }}</view>
                <view class="meet-info">
                  <text style="margin-right: 100rpx">
                    负责人：
                    <text class="stress-text">{{ item.personChargeName }}</text>
                  </text>
<!--                  <text>-->
<!--                    SPU：-->
<!--                    <text class="stress-text">{{ item.productName }}</text>-->
<!--                  </text>-->
                </view>
                <view class="meet-info">
<!--                  <text style="margin-right: 70rpx">-->
<!--                    参会终端：-->
<!--                    <text class="stress-text">{{ item.terminalCount }}</text>-->
<!--                  </text>-->
                  <text>
                    参会人数：
                    <text class="stress-text">{{ item.userCount }}</text>
                  </text>
                </view>
                <view class="meet-time">
                  召开时间：
                  <text class="stress-text">{{ item.teamMeetingTime }}</text>
                </view>
              </view>
            </template>
            <view class="order-btn" @click="handleOrderClick">
              预定会议
            </view>
					</view>
				</scroll-view>
			</view>
		</view>
		<p-tabbar tabbarSelect="work"></p-tabbar>
	</view>
</template>

<script setup>
import WeekFoldCalendar from '@/components/week-fold-calendar/week-fold-calendar.vue';
import { ref, reactive } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { getYearStartDate, formatTimestamp, isFutureDate, isKAManager } from '@/utils/utils.js';
import {academicconferencesinfoPage, bdmteamconferencesinfoPage} from "@/common/api/meeting";
import dayjs from "@/uni_modules/uv-ui-tools/libs/util/dayjs";

const customStyle = {
	fontSize: '38rpx',
	color: '#303030',
	fontWeight: 'normal',
	background: 'transparent'
};

const pagination = reactive({
  currentPage: 1,
  pageSize: 20
});

const loading = ref(false)

const list = ref([])

const minDate = getYearStartDate(); // 限制日历最小时间 (月份选择器，只显示本年的月份)
const currentTime = ref(dayjs().format("YYYY-MM-DD"));
const meetingType = ref(''); //  meetingType=team 团队会议 meetingType=academic 学术会议
onLoad((option) => {
	meetingType.value = option.meetingType || 'academic';
  getList()
});

const getList = () => {
  loading.value = true

  const api = meetingType.value === 'academic' ? academicconferencesinfoPage : bdmteamconferencesinfoPage;

  api({
    conferencesTime: currentTime.value,
    limit: pagination.currentPage,
    size: pagination.pageSize,
  }).then(res => {
    list.value = list.value.concat(res.data.list)
  }).finally(() => {
    loading.value = false
  })
}

const refresh = () => {
  pagination.currentPage = 1
  list.value = []
  getList()
}

const throttledLoadMore = () => {
  if (loading.value)
    return;
  pagination.currentPage++;
  getList();
};


const change = (date) => {
  console.log(date, 'date-change')
	currentTime.value = date;
  refresh()
};

const handleOrderClick = () => {
  uni.navigateTo({
    url: `/pages/subMeeting/meetingBooking/meetingBooking?meetingType=${meetingType.value}`
  })
}

const triggerClient = (item) => {
  if(meetingType.value === 'academic') {
    uni.navigateTo({
      url: `/pages/subMeeting/meetingDetails/meetingDetails?id=${item.id}`
    })
  } else {
    uni.navigateTo({
      url: `/pages/subMeeting/teamMeetingDetails/teamMeetingDetails?id=${item.id}`
    })
  }

}

defineExpose({
  refresh: refresh
})

</script>

<style scoped lang="scss">
@import '@/static/scss/global.scss';
.meetList-panel {
	.page-content {
		@include globalPageStyle();
		display: flex;
		flex-direction: column;
		position: relative;
		.top-panel {
		}
		.bottom-panel {
			flex: 1;
			overflow-y: hidden;
			.list-panel {
				box-sizing: border-box;
				padding: 28rpx 20rpx 44rpx;
				padding-right: 0;
				height: 100%;
				.content-box {
					box-sizing: border-box;
					padding-right: 20rpx;
					height: 100%;
					.client-item {
						padding: 20rpx 28rpx;
						@include cardBgCommonStyle();
						margin-bottom: 24rpx;
						position: relative;
						.meet-status {
							position: absolute;
							padding: 0rpx 12rpx;
							right: 20rpx;
							top: 20rpx;
							@include setBoldFont(28rpx, 40rpx, #fff);
							height: 40rpx;
							border-radius: 54rpx;
							&.PENDING_APPROVAL {
								background: linear-gradient(99deg, #9cb8ff 11.03%, #2d4be9 88.97%);
							}
							&.REJECTED {
								background: linear-gradient(99deg, #ff9c9c 11.03%, #e92d2d 88.97%);
							}
							&.UPCOMING {
								background: linear-gradient(99deg, #ffdd9c 11.03%, #e9bf2d 88.97%);
							}
							&.IN_PROGRESS {
								background: linear-gradient(99deg, #9eff9c 11.03%, #2de94b 88.97%);
							}
							&.COMPLETED {
								background: #b4b9bf;
							}
						}
						.meet-lable {
							@include setBoldFont(24rpx, 40rpx, #4068f5);
						}
						.meet-name {
							margin-bottom: 8rpx;
							@include setBoldFont(28rpx, 44rpx, #2f3133);
						}
						.meet-info,
						.meet-time {
							margin-top: 8rpx;
							@include setlightFont(24rpx, 40rpx, #8d9094);
							.stress-text {
								@include setBoldFont(24rpx, 40rpx, #2f3133);
							}
						}
						.meet-info:first-child {
							margin-right: 50rpx;
						}
					}
				}
			}
		}
    .order-btn {
      position: fixed;
      z-index: 999;
      bottom: 170rpx;
      width: 712rpx;
      height: 64rpx;
      line-height: 64rpx;
      border-radius: 8rpx;
      background: linear-gradient(180deg, #52acff -12%, #263af0 117%);
      @include setlightFont(24rpx, 64rpx, #fff);
      text-align: center;
    }
	}
}
</style>
