<template>
  <!-- <view class="visitLog-panel"></view> -->
  <uv-popup
    ref="popupRef"
    mode="center"
    :safeAreaInsetBottom="false"
    round="16rpx"
    :closeOnClickOverlay="false"
  >
    <view class="visitLog-content">
      <view class="title-panel">
        <text class="title">拜访日志</text>
        <uv-icon
          name="close"
          @click="closeVisitLog"
          color="#BBBDBF"
          size="32rpx"
        ></uv-icon>
      </view>
      <view class="scroll-panel">
        <view class="img-panel">
          <view
            class="sign-img"
            v-for="(item, index) in imageList"
            :key="item"
            @tap="previewImage"
            :data-Index="index"
          >
            <uv-image :src="item" width="100%" height="100%">
              <template v-slot:loading>
                <uv-loading-icon color="red"></uv-loading-icon>
              </template>
            </uv-image>
          </view>
        </view>
        <view class="remark" v-if="visitPurpose"
          >拜访目的:{{ visitPurpose }}</view
        >
        <view class="remark" v-if="visitSumup">拜访总结:{{ visitSumup }}</view>
        <view class="remark" v-if="remarks">备注:{{ remarks }}</view>
      </view>
    </view>
  </uv-popup>
</template>

<script setup>
import { ref, defineEmits, onMounted } from "vue";
const popupRef = ref(null);
const props = defineProps({
  imageList: {
    type: [Array],
    required: true,
  },
  remarks: {
    type: [String],
    required: true,
  },
  visitPurpose: {
    type: [String],
    required: true,
  },
  visitSumup: {
    type: [String],
    required: true,
  },
});

onMounted(async () => {
  popupRef.value.open();
});
// 定义组件的事件，用于向父组件发送消息
const emits = defineEmits(["closeVisitLog"]);
// 关闭这个弹窗
const closeVisitLog = () => {
  console.log(props.imageList, "---imageList");
  emits("closeVisitLog");
};
const previewImage = (event) => {
  const index = event.currentTarget.dataset.index;
  const currentImageUrl = props.imageList[index]; // 获取当前点击图片的 URL

  uni.previewImage({
    urls: props.imageList.map((item) => item), // 获取所有图片的 URL 字符串数组
    current: currentImageUrl, // 使用 URL 字符串作为 current
  });
};
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";
.scroll-panel {
  height: 600rpx;
  overflow-y: auto;
}
.visitLog-content {
  border-radius: 16rpx;
  @include cardBgCommonStyle();
  width: 710rpx;
  // height: 834rpx;
  z-index: 10;
  top: 240rpx;
  box-sizing: border-box;
  padding: 24rpx 24rpx 32rpx;
  .title-panel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 28rpx;
    .title {
      @include setBoldFont(28rpx, 36rpx, $uni-text-color);
    }
  }
  .img-panel {
    display: grid;
    grid-template-columns: 188rpx 188rpx 188rpx; /* 固定每行三列 */
    grid-auto-rows: 188rpx; /* 自动行高 */
    grid-row-gap: 46rpx; /* 行间距 */
    grid-column-gap: 36rpx; /* 列间距 */
    justify-content: start; /* 防止内容少时居中 */
    align-content: start; /* 内容顶部对齐 */
    .sign-img {
      border-radius: 12rpx;
      overflow: hidden;
      width: 188rpx;
      height: 188rpx;
      background: rgba(217, 217, 217, 0.5);
    }
  }
  .remark {
    @include setlightFont(24rpx, 40rpx, #303030);
    margin-top: 24rpx;
  }
}
</style>
