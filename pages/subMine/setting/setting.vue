<template>
	<view>
		<view class="setting-panel">
			<view class="userinfo">
				<view class="user-head">
					<text class="title">头像</text>
					<image class="userhead" src="/static/image/icon/comm/icon_userHead.png" mode=""></image>
				</view>
				<uv-line color="#D9D9D9"></uv-line>
				<view class="user-name">
					<text class="title">姓名</text>
					<text class="desc">{{ UserInfo.name }}</text>
				</view>
				<uv-line color="#D9D9D9"></uv-line>
				<view class="user-dept">
					<text class="title" style="width: 200rpx">辖区</text>
					<text class="desc" style="margin-left: 12rpx; text-align: left">{{ UserInfo.businessUnitIdName }}</text>
				</view>
			</view>

			<view class="login-out" @click="logOut">退出账号</view>
		</view>
		<p-tabbar tabbarSelect="mine"></p-tabbar>
	</view>
</template>

<script setup>
import { useUserStore } from '@/common/store/user';
import { USER_INFO_KEY } from '@/common/const/cache.js';
const useStore = useUserStore();
const customStyle = {
	flex: 1
};
const UserInfo = uni.getStorageSync(USER_INFO_KEY);

const logOut = () => {
	useStore.logout();
};
</script>

<style scoped lang="scss">
@import '@/static/scss/global.scss';

.setting-panel {
	@include globalPageStyle();
	padding: 28rpx 20rpx;

	.userinfo {
		box-sizing: border-box;
		padding: 0 28rpx;

		@include cardBgCommonStyle();
		background: rgba(255, 255, 255, 0.9);
		.user-head,
		.user-name,
		.user-dept {
			display: flex;
			justify-content: space-between;
			min-height: 116rpx;
			align-items: center;

			.desc,
			.title {
				@include setlightFont(28rpx, 36rpx, #1d1d1d);
			}
		}

		.user-head {
			.userhead {
				width: 72rpx;
				height: 72rpx;
			}
		}
	}

	.changePassword {
		margin-top: 28rpx;
		@include cardBgCommonStyle();
		height: 112rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: hidden;

		.text {
			@include setlightFont();
		}
	}

	.login-out {
		height: 84rpx;
		text-align: center;
		@include cardBgCommonStyle();
		background: rgba(255, 255, 255, 0.9);
		@include setBoldFont(28rpx, 112rpx, #4068f5);
		@include absoluteHorizontalCenter();
		bottom: calc(env(safe-area-inset-bottom) + 128rpx);
		width: calc(100% - 40rpx);
		line-height: 84rpx !important;
	}
}

::v-deep .uv-upload__button {
	height: 80rpx !important;
	width: 80rpx !important;
}

::v-deep .uv-upload__wrap__preview {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50% !important;
}
</style>
