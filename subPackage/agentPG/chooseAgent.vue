<template>
  <view class="choose-agent-page">
    <view class="process-content">
      <view>
        <view class="form-item">
          <view class="label">选择代理商</view>
          <view class="select-input" @click="handleSelectAgent">
            <input class="select-text" placeholder="请选择" disabled :value="modelValue" />
            <van-icon color="#8D9094" name="arrow-down" />
          </view>
          <view class="label" style="margin-top: 20rpx;">选择医院</view>
          <view class="select-input" @click="handleSelectHospital">
            <input class="select-text" placeholder="请选择" disabled :value="hospitalValue" />
            <van-icon color="#8D9094" name="arrow-down" />
          </view>
        </view>
      </view>

      <view class="order-btn" @click="handleLaunch">
        开始评估
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad, onShow } from "@dcloudio/uni-app";

const modelValue = ref('');
const hospitalValue = ref('')
const status = ref('')

onLoad((options) => {
  console.log(options)
  status.value = options?.status
})

onShow(()=> {
  uni.$on('updateData',function(data){
    console.log(data)
    if (Object.keys(data).length > 0){
      modelValue.value = data.agentName;
    }
  })
  uni.$on('updateHospitalData',function(data){
    console.log(data)
    if (Object.keys(data).length > 0){
      hospitalValue.value = data.name;
    }
  })
})

const handleLaunch = () => {
  if (!modelValue.value) {
    uni.showToast({
      title: "请选择代理商",
      icon: "none",
    });
    return;
  }
  if (!hospitalValue.value) {
    uni.showToast({
      title: "请选择医院",
      icon: "none",
    });
    return;
  }
  uni.navigateTo({
    url: "/subPackage/agentPG/launch",
  })
}

const handleSelectAgent = () => {
  uni.navigateTo({
    url: `/subPackage/agentPG/selectAgent/selectAgent?status=${status.value}`,
  })
}

const handleSelectHospital = () => {
  if (!modelValue.value) {
    uni.showToast({
      title: "请先选择代理商",
      icon: "none",
    });
    return;
  }
  uni.navigateTo({
    url: `/subPackage/agentPG/selectClientShop/selectClientShop?name=${modelValue.value}&status=${status.value}`,
  })
}
</script>

<style scoped lang="scss">
@import '@/static/scss/global.scss';
.choose-agent-page {
  @include globalPageStyle();
}
.process-content {
  background: #ffffff;
  margin: 20rpx;
  padding: 20rpx;
  border-radius: 16rpx;
  .form-item {
    margin-bottom: 20rpx;
    .label {
      margin-bottom: 12rpx;
      font-weight: 500;
      font-family: "PingFang SC";
      font-style: normal;
      font-size: 28rpx;
      color: #2F3133;
    }
  }
  .form-item:last-child {
    margin-bottom: 0;
  }
  .grouped-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx;
    input {
      flex: 1;
      margin-left: 20rpx;
      border: none;
      outline: none;
      background: none;
    }
  }
  .select-input {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 64rpx;
    padding: 0 24rpx;
    border: 1rpx solid #e8e8e8;
    border-radius: 12rpx;
    background: #fff;
    box-sizing: border-box;
    cursor: pointer;

    .select-text {
      font-size: 26rpx;
      color: #333;
      flex: 1;

      &.placeholder {
        color: #999;
      }
    }

    .select-arrow {
      font-size: 20rpx;
      color: #999;
      transform: rotate(0deg);
      transition: transform 0.3s ease;
    }

    &:active {
      background: #f8f9fa;
    }
  }
  .remark {
    margin-top: 14rpx;
  }
}
.order-btn {
  @include setlightFont(24rpx, 64rpx, #fff);
  background: linear-gradient(180deg, #52acff -12%, #263af0 117%);
  position: fixed;
  bottom: calc(env(safe-area-inset-bottom) + 126rpx);
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  border-radius: 44rpx;
}
</style>