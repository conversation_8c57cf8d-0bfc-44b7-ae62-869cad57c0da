import {
    http
} from '@/common/request/index.js'; // 局部引入

const api = {

    getAgentList: '/wechat/sign/getAgentList',
    getAgentEmployeeList: '/wechat/sign/getAgentEmployeeList',
    saveAgentEmployee: '/wechat/sign/saveAgentEmployee',
    getCoachedUserWechatVo: '/wechat/sign/getCoachedUserWechatVo'
}

/**
 * 查询所有代理商信息
 */
export const getAgentList = (params) => {
    return http.post(api.getAgentList, params)
}
// 查询所有代理商代表名称
export const getAgentEmployeeList = (params) => {
    return http.post(api.getAgentEmployeeList, params)
}
// 保存代理商名称
export const saveAgentEmployee = (params) => {
    return http.post(api.saveAgentEmployee, params)
}
// 查询当前用户下属列表
export const getCoachedUserWechatVo = (params) => {
    return http.post(api.getCoachedUserWechatVo, params)
}