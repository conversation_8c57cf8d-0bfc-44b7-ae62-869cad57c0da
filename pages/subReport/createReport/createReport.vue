<template>
  <view class="createReport-panel">
    <view class="page-content" :class="pageType">
      <view class="create-report">
        <view class="item time">
          <text class="label">计划周期</text>
          <view class="value" @tap="triggerSelectDate">
            <text>{{ dateRange.start }} ~ {{ dateRange.end }}</text>
            <image
              class="r-icon"
              src="../static/icon_right.png"
              mode=""
            ></image>
          </view>
          <!-- 这里要拉起时间选择的弹窗 -->
        </view>

        <template v-if="pageType === 'plan'">
          <view class="tips">
            <text class="label">重点工作</text>
            <text class="value"
              >参考团队指标计划个人本{{
                createReportType === "week" ? "周" : "月"
              }}指标</text
            >
          </view>
          <view class="input-panel" v-for="(item, key) in formData" :key="key">
            <view class="label">
              <text style="color: red" v-if="requiredFields.includes(key)"
                >*</text
              >
              <text>{{ inputLabels[key] }}</text>
            </view>
            <view class="right-panel">
              <input
                v-model="formData[key]"
                type="number"
                placeholder="请输入"
                class="right-panel-input"
                maxlength="30"
              />
            </view>
          </view>
          <view class="remark-panel">
            <view class="title">其他计划说明</view>
            <view class="content">
              <uv-textarea
                v-model="remarkValue"
                placeholder="请填写其他计划说明"
                maxlength="200"
                adjustPosition
                border="none"
              ></uv-textarea>
            </view>
          </view>
        </template>

        <view v-else class="report-content">
          <view class="report-table">
            <!-- 表头 -->
            <view class="table-header">
              <text class="header-item">重点工作</text>
              <text class="header-item">周期目标</text>
              <text class="header-item">实际达成</text>
              <text class="header-item">达成率</text>
            </view>

            <!-- 表格内容 -->
            <view class="table-body">
              <view
                class="table-row"
                v-for="(item, index) in reportData"
                :key="index"
              >
                <text class="row-item">{{ item.taskTypeName }}</text>
                <text class="row-item">{{ item.target }}</text>
                <text class="row-item">{{ item.real }}</text>
                <text class="row-item"
                  >{{
                    ((Number(item.real) / Number(item.target)) * 100).toFixed(
                      2
                    )
                  }}%</text
                >
              </view>
            </view>
          </view>

          <view class="remark-panel">
            <view class="title">问题及反馈</view>
            <view class="content">
              <uv-textarea
                v-model="remarkValue"
                placeholder="请填写备注内容"
                maxlength="200"
                adjustPosition
                border="none"
              ></uv-textarea>
            </view>
          </view>
        </view>
      </view>
      <button
        class="submit-button"
        :style="{ background: isSubmit ? '#aaa' : '' }"
        :disabled="isSubmit"
        @tap="submitForm"
      >
        提交
      </button>
    </view>
    <p-tabbar tabbarSelect="work"></p-tabbar>
    <uv-calendar
      ref="calendar"
      :default-date="tempDate"
      @confirm="calendarConfirm"
      @close="calendarCancel"
    ></uv-calendar>
  </view>
</template>

<script setup>
import { ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { getWeekRange, getMonthRange } from "@/utils/utils";
import {
  getBdmSalesmanTaskPersonVoByTime,
  taskmanagementpersionAdd,
  taskmanagementpersionreportAdd,
  taskmanagementpersionreportAddInfo,
} from "@/common/api/task";
import { useUserStore } from "@/common/store/user";
// 必填字段
const tempDate = ref(new Date()); // 临时交互日期（用于回显）
const confirmedDate = ref(null); // 确认的日期（用于提交数据）
const userStore = useUserStore();
const userInfo = userStore.getUserInfo;
// 控制提交
const isSubmit = ref(false);
const requiredFields = ref([]);
// 表单标签映射
// 假数据
const inputLabels = ref({});

const taskIds = ref({});
// 假数据
const formData = ref({});

const dateRange = ref({ start: "", end: "" });

const remarkValue = ref("");

// 假数据
const reportData = ref([]);

const pageType = ref(""); // "plan" 工作计划 / "report" 工作报告
const createReportType = ref(""); ///week  month

const calendar = ref(null);
const datetimePicker = ref(null);

const load = () => {
  formData.value = {};
  reportData.value = [];
  remarkValue.value = "";
  if (pageType.value == "plan") {
    // 获取动态表单字段数据
    getBdmSalesmanTaskPersonVoByTime({
      userId: userInfo.id,
      postCode: userInfo.posts[0].code,
    }).then((res) => {
      // 根据接口返回数据动态设置表单字段
      if (res.data) {
        // 这里需要根据实际接口返回的数据结构来设置formData和inputLabels
        res.data.forEach((item) => {
          formData.value[item.taskType] = null;
          inputLabels.value[item.taskType] = item.taskName;
          requiredFields.value.push(item.taskType);
        });
      }
    });
  } else {
    taskmanagementpersionreportAddInfo({
      endYearMonthDay: dateRange.value.end,
      userId: userInfo.id,
      startYearMonthDay: dateRange.value.start,
    }).then((res) => {
      reportData.value = res.data?.visitPersonReports;
      if (res.code === 10500) {
        isSubmit.value = true;
      } else {
        isSubmit.value = false;
      }
      console.log(res);
    });
  }
};

onLoad((option) => {
  // 获取参数
  pageType.value = option.pageType;
  createReportType.value = option.createType || "week";

  dateRange.value =
    createReportType.value === "week" ? getWeekRange() : getMonthRange();
  load();

  // 使用对象映射减少 if-else
  const titleMap = {
    report: {
      week: "新建工作周报",
      month: "新建工作月报",
    },
    plan: {
      week: "新建周计划",
      month: "新建月计划",
    },
  };
  // 设置页面标题
  uni.setNavigationBarTitle({
    title: titleMap[pageType.value]?.[createReportType.value] || "新建",
  });
});

/**
 * @description 提交表单
 */
const submitForm = () => {
  if (pageType.value === "plan") {
    const emptyRequiredFields = requiredFields.value.filter(
      (field) => !formData.value[field]
    );
    if (emptyRequiredFields.length > 0) {
      uni.showToast({
        title: `请填写${emptyRequiredFields
          .map((field) => inputLabels.value[field])
          .join("、")}计划`,
        icon: "none",
      });
      return;
    }
    console.log("提交的数据:", formData.value);
    const addSalesmanTaskPersonVisitWechatDtoList = [];
    Object.keys(formData.value).forEach((key) => {
      addSalesmanTaskPersonVisitWechatDtoList.push({
        taskType: key,
        goal: formData.value[key],
      });
    });

    taskmanagementpersionAdd({
      addSalesmanTaskPersonVisitWechatDtoList,
      endYearMonthDay: dateRange.value.end,
      frequency: createReportType.value == "week" ? 1 : 2,
      remark: remarkValue.value,
      startYearMonthDay: dateRange.value.start,
    }).then((res) => {
      if (res.code === 0) {
        uni.showToast({
          title: "新建成功！",
          icon: "none",
        });
        const pages = getCurrentPages();
        const prePage = pages[pages.length - 2];
        prePage.$vm.refresh();
        uni.navigateBack();
      } else {
        uni.showToast({
          title: "新建失败",
          icon: "none",
        });
      }
    });
  } else {
    const visitPersonReports = reportData.value;

    taskmanagementpersionreportAdd({
      visitPersonReports,
      endYearMonthDay: dateRange.value.end,
      frequency: createReportType.value == "week" ? 1 : 2,
      remark: remarkValue.value,
      startYearMonthDay: dateRange.value.start,
    }).then((res) => {
      if (res.code === 0) {
        uni.showToast({
          title: "新建成功！",
          icon: "none",
        });
        const pages = getCurrentPages();
        const prePage = pages[pages.length - 2];
        prePage.$vm.refresh();
        uni.navigateBack();
      } else {
        uni.showToast({
          title: "新建失败",
          icon: "none",
        });
      }
    });
  }
};

const triggerSelectDate = () => {
  calendar.value.open();
};
const calendarCancel = () => {
  tempDate.value = confirmedDate.value;
};
const calendarConfirm = (e) => {
  console.log(e);
  confirmedDate.value = new Date(e[0]);
  // 选择日期后更新日期范围
  dateRange.value =
    createReportType.value === "week"
      ? getWeekRange(e[0])
      : getMonthRange(e[0]);
  load();
};
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";
.createReport-panel {
  .page-content {
    @include globalPageStyle();
    padding: 20rpx 28rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    &.report {
      padding-bottom: 32rpx;
    }
    .create-report {
      @include cardBgCommonStyle();
      padding: 22rpx 8rpx 46rpx;
      .label {
        @include setBoldFont(28rpx, 44rpx, #2f3133);
      }
      .value,
      .right-panel-input {
        @include setlightFont(24rpx, 40rpx, #000);
        text-align: right;
      }
      .tips {
        margin-bottom: 18rpx;
        justify-content: flex-start !important;
        .label {
          @include setBoldFont(24rpx, 40rpx, #2f3133);
        }
        .value {
          margin-left: 16rpx;
          @include setBoldFont(20rpx, 36rpx, #8d9094);
        }
      }
      .item,
      .tips,
      .input-panel {
        padding: 0 20rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .time {
        .value {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          .r-icon {
            width: 15rpx;
            height: 24rpx;
            margin-left: 24.5rpx;
          }
        }
      }

      .input-panel {
        padding: 20rpx 20rpx;
        border-bottom: 2rpx solid #ced4db;
        .right-panel {
          width: 50%;
        }
        &:last-child {
          border: none;
        }
      }
      .remark-panel {
        margin-top: 30rpx;
        padding: 0 20rpx;
        .title {
          @include setBoldFont(28rpx, 44rpx, #2f3133);
          margin-bottom: 4rpx;
        }
        .content {
          height: 340rpx;
          border-radius: 16rpx;
          background-color: #e6ebf0;
          :deep() {
            .uv-textarea {
              background-color: transparent !important;
              height: 100%;
              width: 100%;
              box-sizing: border-box;
              padding: 23rpx 25rpx;
              .uv-textarea__field {
                height: 100% !important;
              }
            }
            .textarea-placeholder {
              @include setlightFont(28rpx, 32rpx, rgba(29, 29, 29, 0.6));
            }
          }
        }
      }
      .report-content {
        margin-top: 42rpx;
        padding: 0 20rpx;
        .remark-panel {
          padding: 0;
          .title {
            margin-bottom: 22rpx;
          }
          .content {
            height: 280rpx;
          }
        }
      }
      .report-table {
        margin-top: 12rpx;
        width: 100%;
        background: #e6ebf0;
        border-radius: 16rpx;
        .table-header {
          display: flex;
          padding: 20rpx 0;
          text-align: center;
          @include setlightFont(24rpx, 40rpx, #8d9094);
          .header-item {
            flex: 1;
            text-align: center;
          }
        }
        .table-body {
          .table-row {
            display: flex;
            padding: 20rpx 0;
            .row-item {
              flex: 1;
              text-align: center;
              @include setBoldFont(28rpx, 44rpx, #2f3133);
              &:first-child {
                white-space: nowrap;
                @include setlightFont(24rpx, 40rpx, #8d9094);
              }
            }

            &:last-child {
              border-bottom: none;
            }
          }
        }
      }
    }
    .submit-button {
      margin-top: 24rpx;
      @include commonButtonStyle();
    }
  }
}
</style>
