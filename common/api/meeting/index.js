import {
	http
} from '@/common/request/index.js'; // 局部引入


const api = {
	academicconferencesinfoPage: '/wechat/academicconferencesinfo/page',
	bdmteamconferencesinfoPage: '/wechat/bdmteamconferencesinfo/page',
	meetingProduct: '/base/productformat/getProductFormatSelect',
	getCrmTerminalWechatVo: '/wechat/academicconferencesinfo/getCrmTerminalWechatVo',
	doctorPage: '/crm/doctor/page',
	addDoctor: '/crm/doctor/add',
	getBdmAcademicUser: '/organization/user/getBdmAcademicUser',
	allDept: '/base/hospitaldepartments/all-tree',
	academicconferencesinfoAdd: '/wechat/academicconferencesinfo/add',
	academicconferencesinfoUpdate: '/wechat/academicconferencesinfo/update',
	academicconferencesinfo: '/wechat/academicconferencesinfo/info',
	updateStatus: '/wechat/academicconferencesinfo/updateStatus',
	teamUpdateStatus: '/wechat/bdmteamconferencesinfo/updateStatus',
	getBdmTeamUserInfoWechatVo: '/wechat/bdmteamconferencesinfo/getBdmTeamUserInfoWechatVo',
	bdmteamconferencesinfoAdd: '/wechat/bdmteamconferencesinfo/add',
	bdmteamconferencesinfoUpdate: '/wechat/bdmteamconferencesinfo/update',
	bdmteamconferencesinfoInfo: '/wechat/bdmteamconferencesinfo/info',
	getApproveUrl: '/thirdinfo/kanionoa/getApproveUrl',
	getBdmConferenceInformationInfo: '/tsl/academicconferencesinfo/getBdmConferenceInformationInfo',
	getBdmConferenceSummarizeInfo: '/tsl/academicconferencesinfo/getBdmConferenceSummarizeInfo',
	getFile: '/system/file',
}

export const getFile = (params) => {
	return http.get(api.getFile, { params });
}
export const getBdmConferenceInformationInfo = (params) => {
	return http.get(api.getBdmConferenceInformationInfo, { params });
}
export const getBdmConferenceSummarizeInfo = (params) => {
	return http.get(api.getBdmConferenceSummarizeInfo, { params });
}
export const academicconferencesinfoPage = (data) => {
	return http.post(api.academicconferencesinfoPage, data);
}
export const bdmteamconferencesinfoPage = (params) => {
	return http.get(api.bdmteamconferencesinfoPage, { params });
}

export const meetingProduct = (params) => {
	return http.get(api.meetingProduct, { params });
}

export const getCrmTerminalWechatVo = (data) => {
	return http.post(api.getCrmTerminalWechatVo, data);
}
export const doctorPage = (params) => {
	return http.get(api.doctorPage, { params });
}

export const addDoctor = (data) => {
	return http.post(api.addDoctor, data);
}
export const getBdmAcademicUser = (data) => {
	return http.post(api.getBdmAcademicUser, data);
}

export const allDept = (params) => {
	return http.get(api.allDept, { params });
}

export const academicconferencesinfoAdd = (data) => {
	return http.post(api.academicconferencesinfoAdd, data);
}

export const academicconferencesinfoUpdate = (data) => {
	return http.post(api.academicconferencesinfoUpdate, data);
}

export const academicconferencesinfo = (params) => {
	return http.get(api.academicconferencesinfo, { params });
}

export const updateStatus = (data) => {
	return http.post(api.updateStatus, data);
}

export const teamUpdateStatus = (data) => {
	return http.post(api.teamUpdateStatus, data);
}
export const getBdmTeamUserInfoWechatVo = (data) => {
	return http.post(api.getBdmTeamUserInfoWechatVo, data);
}
export const bdmteamconferencesinfoAdd = (data) => {
	return http.post(api.bdmteamconferencesinfoAdd, data);
}

export const bdmteamconferencesinfoUpdate = (data) => {
	return http.post(api.bdmteamconferencesinfoUpdate, data);
}

export const bdmteamconferencesinfoInfo = (params) => {
	return http.get(api.bdmteamconferencesinfoInfo, { params });
}

export const getApproveUrl = (params) => {
	return http.get(api.getApproveUrl, { params });
}
