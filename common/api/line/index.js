import {
	http
} from '@/common/request/index.js'; // 局部引入

const api = {
	ListMyOtcSalesmanLinePlan: '/wechat/lineplan/ListMyOtcSalesmanLinePlan',
	ListOtcSalesmanLinePlan: '/wechat/lineplan/ListOtcSalesmanLinePlan',
	getBaiduRouting: '/wechat/lineplan/getBaiduRouting',
	baiduToGaoDe: '/wechat/lineplan/baiduToGaoDe',
	saveOtcSalesmanLinePlanDto: '/wechat/lineplan/saveOtcSalesmanLinePlanDto',
	delOtcSalesmanLinePlanDto: '/wechat/lineplan/delOtcSalesmanLinePlanDto',
}
// 取当前登录用户某一天的路线 
export const ListMyOtcSalesmanLinePlan = (params) => {
	return http.post(api.ListMyOtcSalesmanLinePlan, params)
}
// 取当前登录用户某一天的路线的详细信息(用于编辑模式s)
export const ListOtcSalesmanLinePlan = (params) => {
	return http.post(api.ListOtcSalesmanLinePlan, params)
}
// 百度地图生成路线
export const getBaiduRouting = (params) => {
	return http.post(api.getBaiduRouting, params)
}
// 百度坐标转换高德坐标
export const baiduToGaoDe = (params) => {
	return http.post(api.baiduToGaoDe, params)
}

// 百度坐标转换高德坐标
export const saveOtcSalesmanLinePlanDto = (params) => {
	return http.post(api.saveOtcSalesmanLinePlanDto, params)
}

// 删除相关记录
export const delOtcSalesmanLinePlanDto = (params) => {
	return http.post(api.delOtcSalesmanLinePlanDto, params)
}