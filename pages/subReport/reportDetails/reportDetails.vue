<template>
	<view class="reportDetails-panel">
		<view class="page-content">
			<view class="report-user">
				<view class="name-panel">
					<text class="name">{{ reportDetail.createUserName || userInfo.name }}</text>
					<text class="dept">{{ reportDetail.postName || userInfo.posts[0]?.name }}</text>
				</view>
				<text>{{ reportDetail.departNames }}</text>
			</view>

			<view class="report-infos">
				<view class="time">
					<text class="title-label">计划周期</text>
					<view class="value">
            <text v-if="reportDetail.cycle">{{ reportDetail.cycle }}</text>
						<text v-else>{{ reportDetail.startYearMonthDay }} ~ {{ reportDetail.endYearMonthDay }}</text>
					</view>
				</view>
				<template v-if="pageType === 'plan'">
					<view class="content">
            <view class="list-item item">
              <view class="label">
                <text>重点工作</text>
              </view>
              <view class="label">
                <text>本月目标</text>
              </view>
            </view>
						<view class="list-item item" v-for="(item, key) in formData" :key="key">
							<view class="label">
								<text>{{ inputLabels[key] }}</text>
							</view>
							<view class="right-panel">
								<text>{{ formData[key] }}</text>
							</view>
						</view>
					</view>
					<view class="remak-panel">
						<text class="title">其他计划说明</text>
						<view class="detail">
							<text>{{ remarkValue }}</text>
						</view>
					</view>
				</template>
				<view class="report-content" v-else>
					<view class="report-table">
						<!-- 表头 -->
						<view class="table-header">
							<text class="header-item">重点工作</text>
							<text class="header-item">周期目标</text>
							<text class="header-item">实际达成</text>
							<text class="header-item">达成率</text>
						</view>

						<!-- 表格内容 -->
            <view class="table-body" v-if="pageType === 'plan'">
              <view class="table-row" v-for="(item, index) in reportData" :key="index">
                <text class="row-item">{{ item.task }}</text>
                <text class="row-item">{{ item.target }}</text>
                <text class="row-item">{{ item.achieved }}</text>
                <text class="row-item">{{ item.rate }}</text>
              </view>
            </view>
						<view class="table-body" v-else>
							<view class="table-row" v-for="(item, index) in reportData" :key="index">
								<text class="row-item">{{ item.taskTypeName }}</text>
								<text class="row-item">{{ item.target }}</text>
								<text class="row-item">{{ item.real }}</text>
								<text class="row-item">{{ ((Number(item.real)/ Number(item.target)) * 100).toFixed(2)}}%</text>
							</view>
						</view>
					</view>

					<view class="remark-panel">
						<text class="title">问题及反馈</text>
						<view class="detail">
							<text>{{ remarkValue }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<p-tabbar tabbarSelect="work"></p-tabbar>
	</view>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app';
import {taskmanagementpersionInfo, taskmanagementpersionreportInfo} from "@/common/api/task";
import { ref } from 'vue';
import {useUserStore} from "@/common/store/user";

const inputLabels = ref({});
const formData = ref({
});

const pageType = ref(''); // "plan" 工作计划 / "report" 工作报告
const createReportType = ref(''); ///week  month
const reportDetail = ref({}); // 详情数据
const reportData = ref([]); // 任务列表数据
const remarkValue = ref('')

const userStore = useUserStore()
const userInfo = userStore.getUserInfo

onLoad(async (option) => {
	// 获取参数
	// pageType=report&createType=week
	pageType.value = option.pageType;
	createReportType.value = option.createType || 'week';
	const id = option.id; // 获取传入的id参数

	// 使用对象映射减少 if-else
	const titleMap = {
		report: {
			week: '工作周报',
			month: '工作月报'
		},
		plan: {
			week: '周计划',
			month: '月计划'
		}
	};

	// 设置页面标题
	uni.setNavigationBarTitle({
		title: titleMap[pageType.value]?.[createReportType.value] || '新建'
	});

  if(pageType.value === 'plan') {
    // 获取详情数据
    try {
      const res = await taskmanagementpersionInfo({ id });
      if (res.data) {
        reportDetail.value = res.data;
        // 处理任务列表数据
        if (res.data.dbmSalesmanTaskVisitPersonVoList) {
          reportData.value = res.data.dbmSalesmanTaskVisitPersonVoList.forEach(item => {
            formData.value[item.taskType] = item.goal
            inputLabels.value[item.taskType] = item.taskName
          });
          remarkValue.value = res.data.remark
        }
      }
    } catch (error) {
      console.error('获取详情失败:', error);
      uni.showToast({
        title: '获取详情失败',
        icon: 'none'
      });
    }
  } else {
    try {
      const res = await taskmanagementpersionreportInfo({ id });
      if (res.data) {
        reportData.value = res.data.visitPersonReports;
        reportDetail.value = res.data;
        remarkValue.value = res.data.remark
      }
    } catch (error) {
      console.error('获取详情失败:', error);
      uni.showToast({
        title: '获取详情失败',
        icon: 'none'
      });
    }
  }


});
</script>

<style scoped lang="scss">
@import '@/static/scss/global.scss';
.reportDetails-panel {
	.page-content {
		@include globalPageStyle();
		padding: 20rpx 28rpx;
		.report-user {
			@include cardBgCommonStyle();
			@include setlightFont(20rpx, 36rpx, #8d9094);
			padding: 20rpx 24rpx;
			.name-panel {
				.name {
					@include setBoldFont(28rpx, 44rpx, #1d1d1d);
				}
				.dept {
					margin-left: 14rpx;
					@include setlightFont(20rpx, 26rpx, #fff);
					padding: 0 14rpx;
					border-radius: 8rpx;
					background: #21c369;
				}
			}
		}
		.report-infos {
			margin-top: 24rpx;
			@include cardBgCommonStyle();
			padding: 24rpx 28rpx;

			.time {
				display: flex;
				justify-content: space-between;
				align-items: center;
				.title-label {
					@include setBoldFont(28rpx, 44rpx, #2f3133);
				}
				.value {
					@include setlightFont(24rpx, 40rpx, #000);
					display: flex;
					justify-content: flex-start;
					align-items: center;
					.r-icon {
						width: 15rpx;
						height: 24rpx;
						margin-left: 24.5rpx;
					}
				}
			}

			.content {
				margin-top: 12rpx;
				border-radius: 16rpx;
				background: #e6ebf0;
				box-sizing: 0;
				padding: 20rpx 32rpx 16rpx 20rpx;
				.title {
					@include setBoldFont(28rpx, 44rpx, #2f3133);
					margin-bottom: 36rpx;
				}
				.item {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 40rpx;
					&:last-child {
						margin-bottom: 0;
					}
					.label {
						@include setlightFont(28rpx, 44rpx, #2f3133);
					}
					.right-panel {
						@include setBoldFont(28rpx, 44rpx, #2f3133);
					}
				}
			}
			.remak-panel {
				margin-top: 42rpx;
				.title {
					@include setBoldFont(28rpx, 44rpx, #2f3133);
				}
				.detail {
					border-radius: 16rpx;
					background: #e6ebf0;
					height: 280rpx;
					box-sizing: border-box;
					padding: 10rpx 18rpx;
					@include setlightFont(24rpx, 40rpx, #8d9094);
				}
			}

			.report-content {
				margin-top: 12rpx;
				.report-table {
					margin-top: 12rpx;
					width: 100%;
					background: #e6ebf0;
					border-radius: 16rpx;
					.table-header {
						display: flex;
						padding: 20rpx 0;
						text-align: center;
						@include setlightFont(24rpx, 40rpx, #8d9094);
						.header-item {
							flex: 1;
							text-align: center;
						}
					}
					.table-body {
						.table-row {
							display: flex;
							padding: 20rpx 0;
							.row-item {
								flex: 1;
								text-align: center;
								@include setBoldFont(28rpx, 44rpx, #2f3133);
								&:first-child {
									white-space: nowrap;
									@include setlightFont(24rpx, 40rpx, #8d9094);
								}
							}

							&:last-child {
								border-bottom: none;
							}
						}
					}
				}
				.remark-panel {
					padding: 0;
					margin-top: 42rpx;
					.title {
						@include setBoldFont(28rpx, 44rpx, #2f3133);
						margin-bottom: 22rpx;
					}
					.detail {
						border-radius: 16rpx;
						background: #e6ebf0;
						height: 280rpx;
						box-sizing: border-box;
						padding: 10rpx 18rpx;
						@include setlightFont(24rpx, 40rpx, #8d9094);
					}
				}
			}
		}
	}
}
</style>
