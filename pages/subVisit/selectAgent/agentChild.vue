<template>
  <view class="selectClient-panel">
    <view class="page-content">
      <view class="top-panel">
        <view class="search-panel">
          <p-search
            placeholderText="请输入代理商代表名称"
            @searchSumbit="acceptSearch"
          ></p-search>
        </view>
      </view>
      <view v-if="agentList.length === 0" class="task-panel">
        <image class="empty" src="/static/image/bgs/empty.png" mode=""></image>
        <view class="empty-text">暂无数据</view>
      </view>
      <view v-else class="bottom-panel">
        <scroll-view
          class="list-panel"
          scroll-y="true"
          @scrolltolower="throttledLoadMore"
          lower-threshold="200"
        >
          <view class="content-box">
            <view
              class="person-cont"
              v-for="(item, index) in agentList"
              :key="index"
              @click="clickChild(item)"
            >
              {{ item.name }}
              <text class="tag xue" v-if="item.position === '学术'">学术</text
              ><text class="tag dai" v-if="item.position === '代表'">代表</text>
            </view>
          </view>
        </scroll-view>
      </view>
      <view v-if="loading" class="uv-picker--loading">
        <uv-loading-icon mode="circle"></uv-loading-icon>
      </view>
      <view v-if="visitType !== '2'" class="placeholder-view"></view>
    </view>
    <view v-if="visitType !== '2'" class="btn" @click="toAddAgent"
      >+ 新增代理商代表</view
    >
    <p-tabbar tabbarSelect="work"></p-tabbar>
  </view>
</template>

<script setup>
import { onLoad, onShow } from "@dcloudio/uni-app";
import { ref, reactive } from "vue";
import { getAgentEmployeeList } from "/common/api/agent/index.js";
import { useVisitStore } from "@/common/store/visit";
const loading = ref(false);
const visitStore = useVisitStore();
const pagination = reactive({
  currentPage: 1,
  totalItems: 0,
  pageSize: 20,
});
const keyword = ref("");
const agentList = ref([]);
const visitType = ref(visitStore.$state.visitType);
const toAddAgent = () => {
  uni.navigateTo({
    url: `/pages/subVisit/addAgent/addAgent`,
  });
};
const acceptSearch = (inptKeyword) => {
  keyword.value = inptKeyword;
  console.log("搜索条件：keyword", inptKeyword);
  pagination.currentPage = 1;
  agentList.value = [];
  getList(inptKeyword, visitStore.$state.agent.agentId);
};
const throttledLoadMore = () => {
  if (loading.value) return;
  pagination.currentPage++;
  getList(keyword.value, visitStore.$state.agent.agentId);
};
const clickChild = (item) => {
  console.log(item, "item");
  visitStore.setIsIndex(false);
  visitStore.setAgent({
    ...visitStore.$state.agent,
    ...item,
    agentEmployeeId: item.id,
    agentEmployeeName: item.name,
  });
  if (visitStore.$state.visitType === "3" && visitStore.$state.agent.isOne) {
    uni.reLaunch({
      url: `/pages/subVisit/createVisit/createVisit`,
    });
  } else {
    visitStore.setAgent({
      ...visitStore.$state.agent,
      isOne: false,
    });
    uni.navigateBack({ delta: 2 });
  }
};
const getList = async (name, agentId) => {
  if (loading.value) {
    return;
  }
  loading.value = true;
  try {
    let parmas = {
      limit: pagination.currentPage,
      size: pagination.pageSize,
      agentId: agentId || "",
      keyWord: name || "",
    };
    let { code, data } = await getAgentEmployeeList(parmas);
    loading.value = false;
    if (code == 0) {
      agentList.value = agentList.value.concat(data.list || []);
    }
  } catch (error) {
    console.error("Error:", error);
  }
};
onLoad((option) => {
  console.log(visitStore.$state.agent, "代理商id");

  getList("", visitStore.$state.agent.agentId);
});
onShow(() => {
  agentList.value = [];
  getList("", visitStore.$state.agent.agentId);
});
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";
.placeholder-view {
  width: 100%;
  height: 48rpx;
}
.bottom-panel {
  flex: 1;
  overflow-y: hidden;

  .list-panel {
    box-sizing: border-box;
    padding: 28rpx 20rpx;
    padding-right: 0;
    height: 100%;

    .content-box {
      box-sizing: border-box;
      padding-right: 20rpx;
      height: 100%;
    }
  }
}
.person-cont {
  height: 84rpx;
  display: flex;
  align-items: center;
  padding-left: 24rpx;
  border-radius: 16rpx;
  background: #fff;

  /* 下层投影 */
  box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.12);
  margin-bottom: 20rpx;
  color: var(---Gray7, #2f3133);

  /* 点文本-加粗/14pt bold */
  font-family: "PingFang SC";
  font-size: 28rpx;
  font-style: normal;
  font-weight: 600;
  .tag {
    width: 56rpx;
    height: 36rpx;
    margin-left: 20rpx;
    line-height: 36rpx;
    text-align: center;
    color: var(---white, #fff);

    /* 点文本-加粗/10pt bold */
    font-family: "PingFang SC";
    font-size: 20rpx;
    font-style: normal;
    font-weight: 400;
    border-radius: 8rpx;
  }
  .xue {
    background: #06f;
  }
  .dai {
    background: #f70;
  }
}
.btn {
  display: flex;
  width: 95vw;
  position: fixed;
  bottom: 170rpx;
  margin-left: 20rpx;
  color: #fff;
  font-family: "PingFang SC";
  font-size: 28rpx;
  font-style: normal;
  font-weight: 600;
  height: 72rpx;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  text-align: center;
  border-radius: 8rpx;
  background: linear-gradient(180deg, #52acff -12%, #263af0 117%);

  /* 下层投影 */
  box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.12);
}
.scroll-view {
  padding: 0 20rpx;
  height: 66vh;
  overflow-y: auto;
  margin-top: 20rpx;
}
:deep .business-collapse {
  .van-collapse-item {
    border: none;
  }
  .van-cell {
    border-radius: 16rpx;
    margin-bottom: 20rpx;
  }
  .van-cell:after {
    border: none;
  }
  [class*="van-hairline"]::after {
    border: none;
  }
  .van-collapse-item__content {
    background: transparent;
    padding: 0;
  }
  .van-cell__title {
    color: var(---Gray7, #2f3133);

    /* 点文本-加粗/14pt bold */
    font-family: "PingFang SC";
    font-size: 28rpx;
    font-style: normal;
    font-weight: 600;
  }
  .cont {
    height: 64rpx;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    border-radius: 16rpx;
    background: #fff;
    margin-left: 28rpx;
    padding-left: 24rpx;
    color: var(---Gray7, #2f3133);

    /* 点文本-加粗/14pt bold */
    font-family: "PingFang SC";
    font-size: 28rpx;
    font-style: normal;
    font-weight: 600;
    /* 下层投影 */
    box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.12);
    margin-bottom: 20rpx;
  }
}
.agent-container {
  display: flex;
  width: 370rpx;
  height: 68rpx;
  padding: 10rpx 80rpx;
  justify-content: space-between;
  align-items: flex-end;
  flex-shrink: 0;
  margin: 20rpx auto;
  border-radius: 48rpx;
  background: rgba(255, 255, 255, 0.9);

  /* 下层投影 */
  box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.12);
  .tab-item {
    width: 112rpx;
    align-self: stretch;
    color: var(---Gray6, #8d9094);
    text-align: center;

    /* 点文本-常规/14pt regular */
    font-family: "PingFang SC";
    font-size: 28rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 70rpx; /* 157.143% */
  }
  .active {
    color: #303030;
    font-weight: 600;
    border-bottom: 4rpx solid #4068f5;
  }
}
.selectClient-panel {
  .page-content {
    @include globalPageStyle();
    display: flex;
    flex-direction: column;

    .top-panel {
      padding: 26rpx 24rpx 18rpx;
      box-sizing: border-box;
      background: rgba(255, 255, 255, 0.9);
      /* 下层投影 */
      box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(0, 0, 0, 0.12);
      display: flex;
      flex-direction: column;

      .search-panel {
        ::v-deep .search-panel {
          // display: none;
          background: transparent;
          padding: 0;
          box-shadow: none;
        }
      }
    }
  }
  .empty {
    width: 230rpx;
    height: 200rpx;
    margin: 26vh auto 20rpx;
    display: flex;
  }
  .empty-text {
    color: var(---white, #fff);
    text-align: center;

    /* 点文本-加粗/12pt bold */
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 166.667% */
  }
}
</style>
