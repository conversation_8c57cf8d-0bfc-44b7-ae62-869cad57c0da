<template>
  <view class="agentPG-page">
    <view class="page-content">
      <view class="page-content-title">
        <view style="display: flex;position: relative;width: 100%">
          <view class="process" v-for="(item, index) in processSteps" :key="index" :class="[index + 1 <= now ? 'active' : '', index + 1 === now ? 'nowProess' : '']">
            <text class="process-info">
              {{item}}
            </text>
            <view class="process-img" :class=" 'process-img-' + index" style="position: absolute">
            </view>

            <view v-if="index < processSteps.length - 1" class="process-line" :class="index + 1 < now ? 'line-active' : ''">
            </view>
          </view>
        </view>
      </view>
      <view class=" process-content" >
        <scroll-view class="content-wrapper" scroll-y="true" lower-threshold="200">
          <view v-for="(e, index) in groupedData[now - 1].items" :key="index" class="form-item-box">
            <view class="form-item">
              <view class="label">{{e.name}}</view>
              <view class="select-input" @click="showPicker(e,index)">
                <input class="select-text" placeholder="请选择" disabled :value="modelValue[index]" />
                <van-icon color="#8D9094" name="arrow-down" />
              </view>
              <view class="remark">
                <view class="select-input">
                  <input @change="changeValue" class="select-text" placeholder="请输入具体说明/数值" :value="remarkValue[index]" />
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>

      <view class="page-content-footer">
        <view class="footer-left">
          <view class="prev footer-button" @click="prevStep" :class="{ disabled: now <= 1 }">
            <van-icon name="arrow-left" :color="now <= 1 ? '#CCC' : '#8D9094'"/>
            <text>上一步</text>
          </view>
        </view>

        <view class="save-button" @click="saveDraft">
          <img src="../agentPG/img/saveDraft.png"/>
        </view>

        <view class="footer-right">
          <view class="next footer-button" @click="nextStep" :class="{ disabled: now >= processSteps.length }" v-if="now < processSteps.length">
            <text>下一步</text>
            <van-icon name="arrow" :color="now >= processSteps.length ? '#CCC' : '#8D9094'"/>
          </view>

          <view class="submit footer-button" @click="submit" v-else>
            <text>确认</text>
          </view>
        </view>
      </view>
      <van-popup
          :show="show"
          round
          position="bottom"
          custom-style="width: 750rpx;height: 750rpx;"
          @close="cancel"
      >
        <van-picker
            show-toolbar
            :columns="filteredColumns"
            @confirm="onConfirm"
            @cancel="cancel"
        />
      </van-popup>

      <view v-if="loading" class="uv-picker--loading">
        <uv-loading-icon mode="circle"></uv-loading-icon>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref, computed, onMounted } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import {getInfoWithEnable} from "@/common/api/agentPG";

const modelValue = ref([]);
const remarkValue = ref([])
const show=ref(false);
const now = ref(1);
const filteredColumns = ref([]);
const itemIndex=ref(0)
const loading = ref(false);
// 原始数据
const rawData = ref([]);
// 分组后的数据
const groupedData = ref([])

onLoad(()=> {
  getInfo()
})

const getInfo = async () => {
  const res = await getInfoWithEnable()
  console.log(res)
  rawData.value = res?.data?.bdmAgentEvalTmplIndRelList ?? []
  groupedData.value = groupByTypeCode(rawData.value)
}

// 数据分组函数
const groupByTypeCode = (data) => {
  if (!Array.isArray(data)) return [];
  const groups = data.reduce((acc, item) => {
    const typeCode = item.typeCode;

    if (!acc[typeCode]) {
      acc[typeCode] = {
        typeCode: typeCode,
        typeName: item.typeName,
        typeSort: item.typeSort,
        items: []
      };
    }

    acc[typeCode].items.push(item);
    return acc;
  }, {});

  // 转换为数组并按 typeSort 排序
  return Object.values(groups).sort((a, b) => a.typeSort - b.typeSort);
};

// // 分组后的数据
// const groupedData = computed(() => {
//   console.log(rawData.value.length)
//   return rawData.value.length > 0 ? groupByTypeCode(rawData.value) : [];
// });
// 进度步骤名称
const processSteps = computed(() => {
  return groupedData.value.map(group => group.typeName);
});

const showPicker = (e, index) => {
  itemIndex.value = index;
  filteredColumns.value = e.bdmAgentEvalIndicatorOptionList.map(item => item.name);
  show.value = true;
}

const changeValue = (e) => {
  console.log(e)
}

const cancel = () => {
  show.value = false;
};

const onConfirm = (e) => {
  modelValue.value[itemIndex.value] = e.target.value;
  console.log(modelValue.value)

  show.value = false;
};
// 上一步
const prevStep = () => {
  if (now.value > 1) {
    now.value--;
  }
};

// 下一步
const nextStep = () => {
  if (now.value < processSteps.value.length) {
    now.value++;
  }
};

// 提交
const submit = () => {
  
}

// 提交草稿
const saveDraft = () => {
  console.log(getCurrentStepData)
}

// 获取当前步骤的数据
const getCurrentStepData = computed(() => {
  const currentIndex = now.value - 1;
  return groupedData.value[currentIndex] || null;
});

</script>
<style scoped lang="scss">
@import "@/static/scss/global.scss";
.agentPG-page {
  .page-content {
    @include globalPageStyle();
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;

    .process-content {
      height: calc(100vh - 124rpx - 70rpx - env(safe-area-inset-bottom) - 126rpx - 40rpx); // 总高度 - 进度条高度 - 按钮高度 - 安全区域 - 底部距离 - 额外间距
      box-sizing: border-box;
      padding:20rpx;
      .content-wrapper {
        padding: 20rpx 0 20rpx 20rpx;
        box-sizing: border-box;
        background: #ffffff;
        border-radius: 16rpx;
        height: 100%;
        .form-item-box {
          padding-right: 20rpx;
          margin-bottom: 20rpx;
        }
        .form-item {
          .label {
            margin-bottom: 12rpx;
            font-weight: 500;
            font-family: "PingFang SC";
            font-style: normal;
            font-size: 28rpx;
            color: #2F3133;
          }
        }
        .form-item-box:last-child {
          margin-bottom: 0;
        }
        .grouped-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 20rpx;
          input {
            flex: 1;
            margin-left: 20rpx;
            border: none;
            outline: none;
            background: none;
          }
        }
        .select-input {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 64rpx;
          padding: 0 24rpx;
          border: 1rpx solid #e8e8e8;
          border-radius: 12rpx;
          background: #fff;
          box-sizing: border-box;
          cursor: pointer;

          .select-text {
            font-size: 26rpx;
            color: #333;
            flex: 1;

            &.placeholder {
              color: #999;
            }
          }

          .select-arrow {
            font-size: 20rpx;
            color: #999;
            transform: rotate(0deg);
            transition: transform 0.3s ease;
          }

          &:active {
            background: #f8f9fa;
          }
        }
        .remark {
          margin-top: 14rpx;
        }
      }
    }
    .page-content-title {
      //width: 100%;
      height: 124rpx;
      background: rgba(255, 255, 255, 0.90);
      box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.12);
      padding: 12rpx 0;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      position: relative;
      .process {
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 2;
        position: relative;
        flex: 1 1 0%;
        .process-line {

          &.line-active {
            background: #4068F5;
            width: 100rpx;
            height: 21rpx;
            position: absolute;
            background: url("../agentPG/img/background.png") no-repeat;
            background-size: 100% 100%;
            z-index: 1;
            left: 100rpx;
            top: -30rpx;
          }
        }

        .process-img {
          width: 48rpx;
          height: 48rpx;
          position: absolute;
          top: -46rpx;
          image {
            width: 100%;
            height: 100%;
          }
        }

        .process-img-0 {
          background: url("../agentPG/img/one.png") no-repeat;
          background-size: 100% 100%;
        }
        .process-img-1 {
          background: url("../agentPG/img/two.png") no-repeat;
          background-size: 100% 100%;
        }
        .process-img-2 {
          background: url("../agentPG/img/three.png") no-repeat;
          background-size: 100% 100%;
        }
        .process-img-3 {
          background: url("../agentPG/img/four.png") no-repeat;
          background-size: 100% 100%;
        }
        .process-img-4 {
          background: url("../agentPG/img/five.png") no-repeat;
          background-size: 100% 100%;
        }

        .process-info {
          font-size: 24rpx;
          color: #B4B9BF;
          margin-top: 6rpx;
          text-align: center;
        }
      }
      .active {
        .process-info {
          color: #4068F5;
        }
        .process-img-0 {
          background: url("../agentPG/img/one-active.png") no-repeat;
          background-size: 100% 100%;
        }
        .process-img-1 {
          background: url("../agentPG/img/two-active.png") no-repeat;
          background-size: 100% 100%;
        }
        .process-img-2 {
          background: url("../agentPG/img/three-active.png") no-repeat;
          background-size: 100% 100%;
        }
        .process-img-3 {
          background: url("../agentPG/img/four-active.png") no-repeat;
          background-size: 100% 100%;
        }
        .process-img-4 {
          background: url("../agentPG/img/five-active.png") no-repeat;
          background-size: 100% 100%;
        }
      }

      .nowProess {
        .process-img-0 {
          background: url("../agentPG/img/one-active-now.png") no-repeat !important;
          background-size: 100% 100% !important;
        }
        .process-img-1 {
          background: url("../agentPG/img/two-active-now.png") no-repeat !important;
          background-size: 100% 100% !important;
        }
        .process-img-2 {
          background: url("../agentPG/img/three-active-now.png") no-repeat !important;
          background-size: 100% 100% !important;
        }
        .process-img-3 {
          background: url("../agentPG/img/four-active-now.png") no-repeat !important;
          background-size: 100% 100% !important;
        }
        .process-img-4 {
          background: url("../agentPG/img/five-active-now.png") no-repeat !important;
          background-size: 100% 100% !important;
        }
      }
    }

    .page-content-footer {
      position: fixed;
      bottom: calc(env(safe-area-inset-bottom) + 126rpx);
      left: 50%;
      transform: translateX(-50%);
      width: auto;
      height: 70rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 20rpx;
      background: transparent;
      z-index: 2;

      .footer-left,
      .footer-right {
        flex: 0 0 auto;
      }

      .submit {
        background: #4285f4 !important;
        color: #ffffff !important;
      }

      .footer-button {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 70rpx;
        background: #E6EBF0;
        width: 146rpx;
        height: 70rpx;
        color: #8D9094;
        font-size: 26rpx;
        //cursor: pointer;
        //transition: all 0.3s ease;

        &.disabled {
          background: #F5F5F5;
          color: #CCC;
          visibility: hidden;
        }

        &:not(.disabled):active {
          background: #D0D5DA;
        }
      }

      .save-button {
        width: 346rpx;
        height: 70rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        image {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}


</style>