<script>

import {useUserStore} from "@/common/store/user";
import {USER_INFO_KEY} from "@/common/const/cache";

export default {
	onLaunch: function () {
    console.log('App Launch');
		// uni.hideTabBar();
		// uni.removeStorageSync('selectedIndex');
    const UserInfo = uni.getStorageSync(USER_INFO_KEY);
    if(!UserInfo) {
      uni.reLaunch({
        url: "/pages/login/login"
      })
    }
	},
	onShow: async function () {
	},
	onHide: function () {
		console.log('App Hide');
	}
};
</script>
<style lang="scss">
@import '/wxcomponents/vant/common/index.wxss';
page {
	// height: 100vh;
	height: 100%;
	background: url('/static/image/bgs/bg_page.jpg') no-repeat 100% 100%/100% 100%;
}
</style>
