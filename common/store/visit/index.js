import {
	defineStore
} from 'pinia';

export const useVisitStore = defineStore('visit', {
	state: () => {
		return {
			clientShop: null,
			clientDept: null,
			clientDoctor: null,
			location: null,
			signoutParams: null,
			needRefresh: false,
			selectedProductIds: [],
			selectedTerminalIds: [],
			selectedDeptIds: [],
			selectedSpeakerIds: [],
			selectedResponsiblePersonIds: [],
			visitType: null,
			agent: null,
			siginType: null,
			agentEmployee: null,
			activeTab: 0,
			isScene: true,
			isAgent: true,
			isIndex: false,
			copyObj: null,
			addressCheck: null
		};
	},
	getters: {
	},
	actions: {
		setSelectedProductIds(productIds) {
			this.selectedProductIds = productIds;
		},
		setSelectedTerminalIds(terminalIds) {
			this.selectedTerminalIds = terminalIds;
		},
		setSelectedDeptIds(deptIds) {
			this.selectedDeptIds = deptIds;
		},
		setSelectedSpeakerIds(speakerIds) {
			this.selectedSpeakerIds = speakerIds;
		},
		setSelectedResponsiblePersonIds(responsiblePersonIds) {
			this.selectedResponsiblePersonIds = responsiblePersonIds;
		},
		setNeedRefresh(needRefresh) {
			this.needRefresh = needRefresh;
		},
		setClientShop(clientShop) {
			this.clientShop = clientShop;
		},
		setClientDoctor(clientDoctor) {
			this.clientDoctor = clientDoctor;
		},
		setClientDept(clientDoctor) {
			this.clientDept = clientDoctor;
		},
		setLocation(location) {
			this.location = location;
		},
		setSignoutParams(params) {
			this.signoutParams = params;
		},
		setVisitType(visitType) {
			this.visitType = visitType;
		},
		setAgent(agent) {
			this.agent = agent;
		},
		setSiginType(siginType) {
			this.siginType = siginType;
		},
		setAgentEmployee(agentEmployee) {
			this.agentEmployee = agentEmployee;
		},
		setActiveTab(activeTab) {
			this.activeTab = activeTab;
		},
		setIsScene(isScene) {
			this.isScene = isScene;
		},
		setIsAgent(isAgent) {
			this.isAgent = isAgent;
		},
		setIsIndex(isIndex) {
			this.isIndex = isIndex;
		},
		setCopyObj(copyObj) {
			this.copyObj = copyObj;
		},
		setAddressCheck(addressCheck) {
			this.addressCheck = addressCheck;
		}
	}
});
