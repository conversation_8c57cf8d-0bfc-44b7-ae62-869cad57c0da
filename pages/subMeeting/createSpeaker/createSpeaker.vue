<template>
	<view class="createSpeaker-panel">
		<view class="page-content">
			<view class="create-from">
				<van-cell v-for="(item, key) in meetingTimeLocation" :key="key" :is-link="item.isLink && !isInputField(key)">
					<view slot="title">
						<view class="van-cell-text">
							<text class="stress-text" v-if="key == 'speakerName' || key == 'speakerTerminal' || key == 'speakerDepartment' || key == 'speakerPhone'">*</text>
							{{ item.title }}
						</view>
					</view>
					<input v-if="isInputField(key)" v-model="item.value" placeholder="请输入" class="meeting-input" maxlength="30" />
					<!-- 否则显示文本 -->
					<text v-else>{{ item.label }}</text>
				</van-cell>
			</view>
			<view class="submit-button">保存</view>
		</view>
		<p-tabbar tabbarSelect="work"></p-tabbar>
	</view>
</template>

<script setup>
import { ref } from 'vue';
const meetingTimeLocation = ref({
	speakerName: { title: '讲者姓名', value: '', label: '', isLink: true },
	speakerTerminal: { title: '所属终端', value: '', label: '', isLink: true },
	speakerDepartment: { title: '所属科室', value: '', label: '', isLink: true },
	speakerPhone: { title: '联系方式', value: '', label: '', isLink: true },
	speakerTitle: { title: '职称', value: '', label: '', isLink: true },
	speakerJob: { title: '职务', value: '', label: '', isLink: true },
	speakerIndications: { title: '主治专长', value: '', label: '', isLink: true },
	speakerGender: { title: '性别', value: '', label: '', isLink: true },
	speakerNationality: { title: '民族', value: '', label: '', isLink: true },
	speakerMarital: { title: '婚姻状况', value: '', label: '', isLink: true },
	speakerBirth: { title: '出生日期', value: '', label: '', isLink: true }
});

const isInputField = (key) => ['speakerName', 'speakerPhone', 'speakerIndications'].includes(key);
</script>

<style scoped lang="scss">
@import '@/static/scss/global.scss';
.createSpeaker-panel {
	.page-content {
		@include globalPageStyle();
		padding: 28rpx 20rpx;
		display: flex;
		flex-direction: column;
		overflow-y: auto;
		justify-content: space-between;
		.create-from {
			@include cardBgCommonStyle();
			box-sizing: border-box;
			padding: 0 8rpx;
			:deep(.van-cell) {
				--cell-vertical-padding: 20rpx;
				--cell-horizontal-padding: 28rpx;
				--cell-background-color: transparent;
				border-bottom: 2rpx solid #ced4db;
				.stress-text {
					@include setBoldFont(28rpx, 44rpx, #f00);
					position: absolute;
					left: -14rpx;
				}
				.van-cell-text {
					position: relative;
					@include setBoldFont(28rpx, 44rpx, #2f3133);
				}
				// .van-cell__title {
				// 	// &::before {
				// 	// 	content: '*';
				// 	// 	@include setBoldFont(28rpx, 44rpx, #f00);
				// 	// }
				// }
				.van-cell__value {
					@include setlightFont(28rpx, 44rpx, #8d9094);
				}
				.meeting-theme-input {
					@include setlightFont(28rpx, 44rpx, #8d9094);
				}
			}
		}
		.submit-button {
			margin-top: 166rpx;
			@include commonSquareButtonStyle();
		}
	}
}
</style>
